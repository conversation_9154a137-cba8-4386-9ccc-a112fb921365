{"version": "0.2.0", "configurations": [{"name": "Launch DtDrvApp.Api", "type": "coreclr", "request": "launch", "preLaunchTask": "build-with-cleanup", "program": "${workspaceFolder}/src/DtDrvApp.Api/bin/Debug/net8.0/DtDrvApp.Api.dll", "args": [], "cwd": "${workspaceFolder}/src/DtDrvApp.Api", "console": "internalConsole", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch DtDrvApp.Api (Console)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-with-cleanup", "program": "${workspaceFolder}/src/DtDrvApp.Api/bin/Debug/net8.0/DtDrvApp.Api.dll", "args": [], "cwd": "${workspaceFolder}/src/DtDrvApp.Api", "console": "externalTerminal", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, {"name": "Attach to Process", "type": "coreclr", "request": "attach"}]}