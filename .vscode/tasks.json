{"version": "2.0.0", "tasks": [{"label": "build", "command": "/home/<USER>/dotnet", "type": "process", "args": ["build", "${workspaceFolder}/DtDrvApp.Core.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "publish", "command": "/home/<USER>/dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/DtDrvApp.Core.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "/home/<USER>/dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/DtDrvApp.Api"], "problemMatcher": "$msCompile"}, {"label": "clean", "command": "/home/<USER>/dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/DtDrvApp.Core.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "test", "command": "/home/<USER>/dotnet", "type": "process", "args": ["test", "${workspaceFolder}/DtDrvApp.Core.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "cleanup-processes", "type": "shell", "command": "pkill -f 'DtDrvApp.Api' || true", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "build-with-cleanup", "dependsOrder": "sequence", "dependsOn": ["cleanup-processes", "build"], "group": {"kind": "build", "isDefault": false}}]}