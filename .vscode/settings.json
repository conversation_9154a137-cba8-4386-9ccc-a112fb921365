{"dotnet.defaultSolution": "DtDrvApp.Core.sln", "dotnet.server.path": "/home/<USER>/dotnet", "omnisharp.dotNetCliPaths": ["/home/<USER>/dotnet"], "omnisharp.path": "latest", "files.exclude": {"**/bin": true, "**/obj": true}, "search.exclude": {"**/bin": true, "**/obj": true}, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}