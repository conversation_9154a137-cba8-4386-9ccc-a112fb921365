#!/bin/bash

# DtDrvApp.Core 构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 .NET SDK
check_dotnet() {
    log_info "Checking .NET SDK..."
    if ! command -v dotnet &> /dev/null; then
        log_error ".NET SDK not found. Please install .NET 8.0 SDK."
        exit 1
    fi
    
    DOTNET_VERSION=$(dotnet --version)
    log_success ".NET SDK version: $DOTNET_VERSION"
}

# 清理构建输出
clean() {
    log_info "Cleaning build outputs..."
    dotnet clean
    find . -name "bin" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "obj" -type d -exec rm -rf {} + 2>/dev/null || true
    log_success "Clean completed"
}

# 还原依赖
restore() {
    log_info "Restoring dependencies..."
    dotnet restore
    log_success "Dependencies restored"
}

# 构建项目
build() {
    log_info "Building project..."
    dotnet build --configuration Release --no-restore
    log_success "Build completed"
}

# 运行测试
test() {
    log_info "Running tests..."
    dotnet test --configuration Release --no-build --verbosity normal
    log_success "Tests completed"
}

# 发布项目
publish() {
    local runtime=${1:-"linux-x64"}
    local output_dir="./publish/$runtime"
    
    log_info "Publishing for runtime: $runtime"
    dotnet publish src/DtDrvApp.Api/DtDrvApp.Api.csproj \
        --configuration Release \
        --runtime $runtime \
        --self-contained true \
        --output $output_dir \
        /p:PublishSingleFile=true \
        /p:PublishTrimmed=true
    
    log_success "Published to: $output_dir"
}

# Docker 构建
docker_build() {
    log_info "Building Docker image..."
    docker build -t dtdrvapp-core:latest .
    log_success "Docker image built: dtdrvapp-core:latest"
}

# 显示帮助信息
show_help() {
    echo "DtDrvApp.Core Build Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  clean                 Clean build outputs"
    echo "  restore              Restore dependencies"
    echo "  build                Build the project"
    echo "  test                 Run tests"
    echo "  publish [runtime]    Publish the project (default: linux-x64)"
    echo "  docker               Build Docker image"
    echo "  all                  Run clean, restore, build, and test"
    echo "  help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all"
    echo "  $0 publish win-x64"
    echo "  $0 docker"
}

# 主函数
main() {
    case "${1:-all}" in
        clean)
            check_dotnet
            clean
            ;;
        restore)
            check_dotnet
            restore
            ;;
        build)
            check_dotnet
            build
            ;;
        test)
            check_dotnet
            test
            ;;
        publish)
            check_dotnet
            publish $2
            ;;
        docker)
            docker_build
            ;;
        all)
            check_dotnet
            clean
            restore
            build
            test
            log_success "All tasks completed successfully!"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
