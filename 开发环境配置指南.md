# DtDrvApp.Core 开发环境配置指南

## 1. 必需软件安装

### 1.1 .NET 8.0 SDK
```bash
# 检查是否已安装
dotnet --version

# 如果未安装，请从以下地址下载：
# https://dotnet.microsoft.com/download/dotnet/8.0
```

### 1.2 开发工具（选择其一）

#### 选项A: Visual Studio 2022 (推荐Windows用户)
- 下载地址: https://visualstudio.microsoft.com/vs/
- 安装时选择".NET 桌面开发"和"ASP.NET 和 Web 开发"工作负载

#### 选项B: Visual Studio Code (跨平台)
- 下载地址: https://code.visualstudio.com/
- 安装以下扩展：
  - C# Dev Kit
  - .NET Extension Pack
  - Docker (可选)

#### 选项C: JetBrains Rider (商业软件)
- 下载地址: https://www.jetbrains.com/rider/

### 1.3 数据库（选择其一）

#### 选项A: SQL Server (推荐)
```bash
# Windows: 下载SQL Server Express
# https://www.microsoft.com/sql-server/sql-server-downloads

# Linux: 使用Docker
docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=YourStrong@Passw0rd" \
   -p 1433:1433 --name sqlserver \
   -d mcr.microsoft.com/mssql/server:2022-latest
```

#### 选项B: MySQL
```bash
# 使用Docker
docker run --name mysql -e MYSQL_ROOT_PASSWORD=YourStrong@Passw0rd \
   -e MYSQL_DATABASE=DtDrvApp -p 3306:3306 -d mysql:8.0
```

### 1.4 Docker (可选，用于容器化部署)
- 下载地址: https://www.docker.com/products/docker-desktop

## 2. 项目打开方式

### 2.1 使用Visual Studio 2022
1. 启动Visual Studio 2022
2. 选择"打开项目或解决方案"
3. 导航到 `DtDrvApp.Core` 文件夹
4. 选择 `DtDrvApp.Core.sln` 文件
5. 点击"打开"

### 2.2 使用Visual Studio Code
1. 启动VS Code
2. 选择"文件" > "打开文件夹"
3. 选择 `DtDrvApp.Core` 文件夹
4. VS Code会自动识别.NET项目

### 2.3 使用命令行
```bash
# 进入项目目录
cd DtDrvApp.Core

# 还原依赖包
dotnet restore

# 构建项目
dotnet build

# 运行项目
cd src/DtDrvApp.Api
dotnet run
```

## 3. 数据库配置

### 3.1 配置连接字符串
编辑 `src/DtDrvApp.Api/appsettings.json`:

#### SQL Server配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=DtDrvApp;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

#### MySQL配置
```json
{
  "ConnectionStrings": {
    "MySqlConnection": "Server=localhost;Port=3306;Database=DtDrvApp;Uid=root;Pwd=YourStrong@Passw0rd;"
  }
}
```

### 3.2 初始化数据库
```bash
# 运行数据库初始化脚本
# SQL Server
sqlcmd -S localhost -i scripts/init-database.sql

# 或者使用EF Core迁移（如果配置了）
dotnet ef database update --project src/DtDrvApp.Infrastructure
```

## 4. 运行项目

### 4.1 开发模式运行
```bash
cd src/DtDrvApp.Api
dotnet run
```

### 4.2 生产模式运行
```bash
cd src/DtDrvApp.Api
dotnet run --environment Production
```

### 4.3 使用Docker运行
```bash
# 构建Docker镜像
docker build -t dtdrvapp-core .

# 运行容器
docker run -p 23456:23456 -p 5000:5000 dtdrvapp-core

# 或使用Docker Compose
docker-compose up -d
```

## 5. 验证运行状态

### 5.1 检查服务状态
- HTTP API: http://localhost:5000
- HTTPS API: https://localhost:5001
- TCP服务: localhost:23456
- 健康检查: http://localhost:5000/health
- API文档: http://localhost:5000/swagger

### 5.2 查看日志
```bash
# 日志文件位置
ls logs/

# 实时查看日志
tail -f logs/dtdrvapp-*.log
```

## 6. 常见问题解决

### 6.1 端口占用
```bash
# 检查端口占用
netstat -an | grep :23456
netstat -an | grep :5000

# 修改端口配置
# 编辑 appsettings.json 中的 Communication.DataPort
```

### 6.2 数据库连接失败
1. 检查数据库服务是否启动
2. 验证连接字符串是否正确
3. 确认防火墙设置
4. 检查数据库用户权限

### 6.3 依赖包问题
```bash
# 清理并重新还原
dotnet clean
dotnet restore
dotnet build
```

## 7. 开发调试

### 7.1 设置断点调试
- Visual Studio: 直接在代码行点击设置断点，按F5启动调试
- VS Code: 设置断点后，按F5选择".NET Core Launch"

### 7.2 查看日志输出
- 控制台会显示结构化日志
- 文件日志保存在 `logs/` 目录
- 可以调整 `appsettings.json` 中的日志级别

### 7.3 性能分析
```bash
# 使用dotnet工具进行性能分析
dotnet add package Microsoft.AspNetCore.Diagnostics.HealthChecks
```

## 8. 测试运行

### 8.1 运行单元测试
```bash
# 运行所有测试
dotnet test

# 运行特定测试项目
dotnet test tests/DtDrvApp.Tests/

# 生成测试报告
dotnet test --logger trx --results-directory TestResults
```

### 8.2 集成测试
```bash
# 运行集成测试
dotnet test --filter Category=Integration
```

## 9. 生产部署准备

### 9.1 发布应用
```bash
# 发布为自包含应用
dotnet publish src/DtDrvApp.Api -c Release -r linux-x64 --self-contained true -o ./publish

# 发布为框架依赖应用
dotnet publish src/DtDrvApp.Api -c Release -o ./publish
```

### 9.2 配置生产环境
1. 复制 `appsettings.Production.json` 并修改配置
2. 设置环境变量 `ASPNETCORE_ENVIRONMENT=Production`
3. 配置HTTPS证书
4. 设置日志轮转和监控

这样您就可以成功打开和运行转换后的DtDrvApp.Core项目了！
