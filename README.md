# DtDrvApp.Core

DtDrvApp.Core 是一个基于 .NET 8.0 的企业级数据服务应用程序，从原有的 C++ 项目转换而来。该项目提供数据查询、用户管理、配置管理等核心功能，支持多种数据库和高并发网络通信。

## 项目架构

```
DtDrvApp.Core/
├── src/
│   ├── DtDrvApp.Core/           # 核心业务逻辑层
│   │   ├── Configuration/       # 配置选项类
│   │   ├── Entities/           # 实体模型
│   │   ├── Interfaces/         # 接口定义
│   │   └── Services/           # 核心服务
│   ├── DtDrvApp.Infrastructure/ # 基础设施层
│   │   ├── Data/               # 数据访问
│   │   └── Services/           # 基础设施服务
│   └── DtDrvApp.Api/           # API 层和应用程序入口
├── tests/
│   └── DtDrvApp.Tests/         # 单元测试
└── README.md
```

## 核心功能

### 1. 网络通信
- **TCP 服务器**: 支持 IPv4/IPv6 双栈
- **并发处理**: 基于 Task 的异步处理模型
- **连接管理**: 自动连接池管理和资源清理

### 2. 用户认证
- **多步认证**: 支持用户名/密码分步验证
- **加密传输**: DES/3DES 加密支持
- **会话管理**: 安全的会话状态管理

### 3. 数据访问
- **多数据库支持**: SQL Server、MySQL/OceanBase
- **连接池**: Entity Framework Core 连接池
- **批量操作**: 高性能批量数据导入

### 4. 命令处理
- **命令分发**: 基于命令字的请求路由
- **插件化**: 可扩展的命令处理器架构
- **异步处理**: 全异步命令处理流程

## 技术栈

- **.NET 8.0**: 最新的 .NET 平台
- **Entity Framework Core**: ORM 框架
- **Serilog**: 结构化日志记录
- **ASP.NET Core**: Web 框架和依赖注入
- **xUnit**: 单元测试框架

## 快速开始

### 环境要求

- .NET 8.0 SDK
- SQL Server 2019+ 或 MySQL 8.0+
- Visual Studio 2022 或 VS Code

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd DtDrvApp.Core
   ```

2. **还原依赖**
   ```bash
   dotnet restore
   ```

3. **配置数据库**
   
   编辑 `src/DtDrvApp.Api/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=DtDrvApp;Trusted_Connection=true;TrustServerCertificate=true;"
     }
   }
   ```

4. **运行应用**
   ```bash
   cd src/DtDrvApp.Api
   dotnet run
   ```

### Docker 部署

1. **构建镜像**
   ```bash
   docker build -t dtdrvapp-core .
   ```

2. **运行容器**
   ```bash
   docker run -d -p 23456:23456 -p 5000:5000 dtdrvapp-core
   ```

## 配置说明

### 通信配置
```json
{
  "Communication": {
    "DataPort": 23456,
    "IsIPv6Enable": false,
    "Key": "b374e04c3480d9cb17dd5c0f02dbee66"
  }
}
```

### 数据库配置
```json
{
  "DatabaseSettings": {
    "DisplaySql": true,
    "CommandTimeout": 30,
    "MaxPoolSize": 100
  }
}
```

### 日志配置
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/dtdrvapp-.log",
          "rollingInterval": "Day"
        }
      }
    ]
  }
}
```

## 开发指南

### 添加新的命令处理器

1. **创建处理器类**
   ```csharp
   public class MyCommandHandler : ICommandHandler
   {
       public byte CommandId => 0x10;
       
       public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken)
       {
           // 处理逻辑
           return new CommandResponse { Success = true };
       }
   }
   ```

2. **注册服务**
   ```csharp
   builder.Services.AddScoped<ICommandHandler, MyCommandHandler>();
   ```

### 扩展数据访问

1. **创建实体类**
   ```csharp
   public class MyEntity
   {
       public int Id { get; set; }
       public string Name { get; set; }
   }
   ```

2. **添加到 DbContext**
   ```csharp
   public DbSet<MyEntity> MyEntities { get; set; }
   ```

## 测试

### 运行单元测试
```bash
dotnet test
```

### 运行集成测试
```bash
dotnet test --filter Category=Integration
```

### 性能测试
```bash
dotnet test --filter Category=Performance
```

## 部署

### Windows 服务
```bash
dotnet publish -c Release -r win-x64 --self-contained
sc create DtDrvAppCore binPath="C:\path\to\DtDrvApp.Api.exe"
```

### Linux 系统服务
```bash
dotnet publish -c Release -r linux-x64 --self-contained
sudo systemctl enable dtdrvapp-core.service
```

## 监控和运维

### 健康检查
- HTTP 端点: `GET /health`
- 数据库连接检查
- 服务状态监控

### 日志管理
- 结构化日志记录
- 日志级别控制
- 文件滚动和清理

### 性能监控
- 连接数统计
- 请求处理时间
- 数据库查询性能

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查连接字符串配置
   - 验证数据库服务状态
   - 确认防火墙设置

2. **端口占用**
   - 检查端口 23456 是否被占用
   - 修改配置文件中的端口设置

3. **认证失败**
   - 检查用户表数据
   - 验证密码加密设置

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
