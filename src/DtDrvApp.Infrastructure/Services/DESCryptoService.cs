using System.Security.Cryptography;
using System.Text;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// DES加密服务实现 - 对应C++中的CDESCrypto
/// </summary>
public class DESCryptoService : ICryptoService
{
    private readonly ILogger<DESCryptoService> _logger;
    private readonly byte[] _defaultIV = new byte[8]; // 默认IV为全零

    public DESCryptoService(ILogger<DESCryptoService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// DES加密
    /// </summary>
    public string DesEncrypt(string plainText, string key, Core.Interfaces.CipherMode mode = Core.Interfaces.CipherMode.ECB, Core.Interfaces.PaddingMode paddingMode = Core.Interfaces.PaddingMode.PKCS7)
    {
        try
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            return mode switch
            {
                Core.Interfaces.CipherMode.ECB or Core.Interfaces.CipherMode.General => DesEncryptECB(plainText, key, paddingMode),
                Core.Interfaces.CipherMode.CBC => DesEncryptCBC(plainText, key, paddingMode),
                Core.Interfaces.CipherMode.CFB => DesEncryptCFB(plainText, key),
                _ => throw new NotSupportedException($"Cipher mode {mode} is not supported")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DES encryption failed for mode {Mode}", mode);
            throw;
        }
    }

    /// <summary>
    /// DES解密
    /// </summary>
    public string DesDecrypt(string cipherText, string key, Core.Interfaces.CipherMode mode = Core.Interfaces.CipherMode.ECB, Core.Interfaces.PaddingMode paddingMode = Core.Interfaces.PaddingMode.PKCS7)
    {
        try
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            return mode switch
            {
                Core.Interfaces.CipherMode.ECB or Core.Interfaces.CipherMode.General => DesDecryptECB(cipherText, key, paddingMode),
                Core.Interfaces.CipherMode.CBC => DesDecryptCBC(cipherText, key, paddingMode),
                Core.Interfaces.CipherMode.CFB => DesDecryptCFB(cipherText, key),
                _ => throw new NotSupportedException($"Cipher mode {mode} is not supported")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DES decryption failed for mode {Mode}", mode);
            throw;
        }
    }

    /// <summary>
    /// 3DES加密
    /// </summary>
    public string TripleDesEncrypt(string plainText, string key, Core.Interfaces.CipherMode mode = Core.Interfaces.CipherMode.ECB, Core.Interfaces.PaddingMode paddingMode = Core.Interfaces.PaddingMode.PKCS7)
    {
        try
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            return mode switch
            {
                Core.Interfaces.CipherMode.TripleECB => TripleDesEncryptECB(plainText, key, paddingMode),
                Core.Interfaces.CipherMode.TripleCBC => TripleDesEncryptCBC(plainText, key, paddingMode),
                _ => throw new NotSupportedException($"3DES cipher mode {mode} is not supported")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "3DES encryption failed for mode {Mode}", mode);
            throw;
        }
    }

    /// <summary>
    /// 3DES解密
    /// </summary>
    public string TripleDesDecrypt(string cipherText, string key, Core.Interfaces.CipherMode mode = Core.Interfaces.CipherMode.ECB, Core.Interfaces.PaddingMode paddingMode = Core.Interfaces.PaddingMode.PKCS7)
    {
        try
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            return mode switch
            {
                Core.Interfaces.CipherMode.TripleECB => TripleDesDecryptECB(cipherText, key, paddingMode),
                Core.Interfaces.CipherMode.TripleCBC => TripleDesDecryptCBC(cipherText, key, paddingMode),
                _ => throw new NotSupportedException($"3DES cipher mode {mode} is not supported")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "3DES decryption failed for mode {Mode}", mode);
            throw;
        }
    }

    /// <summary>
    /// DES ECB模式加密
    /// </summary>
    private string DesEncryptECB(string plainText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var des = DES.Create();
        des.Key = PrepareKey(key, 8);
        des.Mode = System.Security.Cryptography.CipherMode.ECB;
        des.Padding = ConvertPaddingMode(paddingMode);

        using var encryptor = des.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// DES ECB模式解密
    /// </summary>
    private string DesDecryptECB(string cipherText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var des = DES.Create();
        des.Key = PrepareKey(key, 8);
        des.Mode = System.Security.Cryptography.CipherMode.ECB;
        des.Padding = ConvertPaddingMode(paddingMode);

        using var decryptor = des.CreateDecryptor();
        var cipherBytes = Convert.FromBase64String(cipherText);
        var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }

    /// <summary>
    /// DES CBC模式加密
    /// </summary>
    private string DesEncryptCBC(string plainText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var des = DES.Create();
        des.Key = PrepareKey(key, 8);
        des.IV = _defaultIV;
        des.Mode = System.Security.Cryptography.CipherMode.CBC;
        des.Padding = ConvertPaddingMode(paddingMode);

        using var encryptor = des.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// DES CBC模式解密
    /// </summary>
    private string DesDecryptCBC(string cipherText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var des = DES.Create();
        des.Key = PrepareKey(key, 8);
        des.IV = _defaultIV;
        des.Mode = System.Security.Cryptography.CipherMode.CBC;
        des.Padding = ConvertPaddingMode(paddingMode);

        using var decryptor = des.CreateDecryptor();
        var cipherBytes = Convert.FromBase64String(cipherText);
        var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }

    /// <summary>
    /// DES CFB模式加密
    /// </summary>
    private string DesEncryptCFB(string plainText, string key)
    {
        using var des = DES.Create();
        des.Key = PrepareKey(key, 8);
        des.IV = _defaultIV;
        des.Mode = System.Security.Cryptography.CipherMode.CFB;
        des.Padding = System.Security.Cryptography.PaddingMode.None;

        using var encryptor = des.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// DES CFB模式解密
    /// </summary>
    private string DesDecryptCFB(string cipherText, string key)
    {
        using var des = DES.Create();
        des.Key = PrepareKey(key, 8);
        des.IV = _defaultIV;
        des.Mode = System.Security.Cryptography.CipherMode.CFB;
        des.Padding = System.Security.Cryptography.PaddingMode.None;

        using var decryptor = des.CreateDecryptor();
        var cipherBytes = Convert.FromBase64String(cipherText);
        var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }

    /// <summary>
    /// 3DES ECB模式加密
    /// </summary>
    private string TripleDesEncryptECB(string plainText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var tripleDes = TripleDES.Create();
        tripleDes.Key = PrepareKey(key, 24);
        tripleDes.Mode = System.Security.Cryptography.CipherMode.ECB;
        tripleDes.Padding = ConvertPaddingMode(paddingMode);

        using var encryptor = tripleDes.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 3DES ECB模式解密
    /// </summary>
    private string TripleDesDecryptECB(string cipherText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var tripleDes = TripleDES.Create();
        tripleDes.Key = PrepareKey(key, 24);
        tripleDes.Mode = System.Security.Cryptography.CipherMode.ECB;
        tripleDes.Padding = ConvertPaddingMode(paddingMode);

        using var decryptor = tripleDes.CreateDecryptor();
        var cipherBytes = Convert.FromBase64String(cipherText);
        var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }

    /// <summary>
    /// 3DES CBC模式加密
    /// </summary>
    private string TripleDesEncryptCBC(string plainText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var tripleDes = TripleDES.Create();
        tripleDes.Key = PrepareKey(key, 24);
        tripleDes.IV = _defaultIV;
        tripleDes.Mode = System.Security.Cryptography.CipherMode.CBC;
        tripleDes.Padding = ConvertPaddingMode(paddingMode);

        using var encryptor = tripleDes.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 3DES CBC模式解密
    /// </summary>
    private string TripleDesDecryptCBC(string cipherText, string key, Core.Interfaces.PaddingMode paddingMode)
    {
        using var tripleDes = TripleDES.Create();
        tripleDes.Key = PrepareKey(key, 24);
        tripleDes.IV = _defaultIV;
        tripleDes.Mode = System.Security.Cryptography.CipherMode.CBC;
        tripleDes.Padding = ConvertPaddingMode(paddingMode);

        using var decryptor = tripleDes.CreateDecryptor();
        var cipherBytes = Convert.FromBase64String(cipherText);
        var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }

    /// <summary>
    /// Base64编码
    /// </summary>
    public string Base64Encode(byte[] data)
    {
        return Convert.ToBase64String(data);
    }

    /// <summary>
    /// Base64编码
    /// </summary>
    public string Base64Encode(string text)
    {
        var bytes = Encoding.UTF8.GetBytes(text);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Base64解码
    /// </summary>
    public byte[] Base64Decode(string base64Text)
    {
        return Convert.FromBase64String(base64Text);
    }

    /// <summary>
    /// Base64解码为字符串
    /// </summary>
    public string Base64DecodeToString(string base64Text)
    {
        var bytes = Convert.FromBase64String(base64Text);
        return Encoding.UTF8.GetString(bytes);
    }

    /// <summary>
    /// MD5哈希
    /// </summary>
    public string Md5Hash(string input)
    {
        var bytes = Encoding.UTF8.GetBytes(input);
        return Md5Hash(bytes);
    }

    /// <summary>
    /// MD5哈希
    /// </summary>
    public string Md5Hash(byte[] data)
    {
        using var md5 = MD5.Create();
        var hashBytes = md5.ComputeHash(data);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// SHA256哈希
    /// </summary>
    public string Sha256Hash(string input)
    {
        using var sha256 = SHA256.Create();
        var bytes = Encoding.UTF8.GetBytes(input);
        var hashBytes = sha256.ComputeHash(bytes);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    public string GenerateRandomKey(int length = 8)
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes)[..length];
    }

    /// <summary>
    /// 生成随机IV
    /// </summary>
    public byte[] GenerateRandomIV(int length = 8)
    {
        using var rng = RandomNumberGenerator.Create();
        var iv = new byte[length];
        rng.GetBytes(iv);
        return iv;
    }

    /// <summary>
    /// 准备密钥（填充或截断到指定长度）
    /// </summary>
    private static byte[] PrepareKey(string key, int keyLength)
    {
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var result = new byte[keyLength];
        
        if (keyBytes.Length >= keyLength)
        {
            Array.Copy(keyBytes, result, keyLength);
        }
        else
        {
            Array.Copy(keyBytes, result, keyBytes.Length);
            // 剩余部分用零填充
        }
        
        return result;
    }

    /// <summary>
    /// 转换填充模式
    /// </summary>
    private static System.Security.Cryptography.PaddingMode ConvertPaddingMode(Core.Interfaces.PaddingMode paddingMode)
    {
        return paddingMode switch
        {
            Core.Interfaces.PaddingMode.None => System.Security.Cryptography.PaddingMode.None,
            Core.Interfaces.PaddingMode.PKCS7 => System.Security.Cryptography.PaddingMode.PKCS7,
            Core.Interfaces.PaddingMode.Zeros => System.Security.Cryptography.PaddingMode.Zeros,
            Core.Interfaces.PaddingMode.ANSIX923 => System.Security.Cryptography.PaddingMode.ANSIX923,
            Core.Interfaces.PaddingMode.ISO10126 => System.Security.Cryptography.PaddingMode.ISO10126,
            _ => System.Security.Cryptography.PaddingMode.PKCS7
        };
    }
}
