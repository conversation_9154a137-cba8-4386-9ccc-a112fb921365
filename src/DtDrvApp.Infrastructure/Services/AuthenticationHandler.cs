using System.Security.Cryptography;
using System.Text;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// 认证处理器实现 - 对应C++中的CSockAuthenDeal
/// </summary>
public class AuthenticationHandler : IAuthenticationHandler
{
    private readonly ILogger<AuthenticationHandler> _logger;
    private readonly ICryptoService _cryptoService;
    
    private IDataAccess? _dataAccess;
    private ISocketHandler? _socketHandler;
    private readonly Dictionary<string, string> _randomStrings = new();

    public AuthenticationHandler(
        ILogger<AuthenticationHandler> logger,
        ICryptoService cryptoService)
    {
        _logger = logger;
        _cryptoService = cryptoService;
    }

    /// <summary>
    /// 初始化认证处理器
    /// </summary>
    public void Initialize(IDataAccess dataAccess, ISocketHandler socketHandler)
    {
        _dataAccess = dataAccess ?? throw new ArgumentNullException(nameof(dataAccess));
        _socketHandler = socketHandler ?? throw new ArgumentNullException(nameof(socketHandler));
    }

    /// <summary>
    /// 处理认证
    /// </summary>
    public async Task<AuthenticationResult> HandleAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            if (data == null || length <= 0)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid authentication data"
                };
            }

            _logger.LogDebug("Processing authentication for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 解析认证数据
            var authData = ParseAuthenticationData(data, length);
            if (authData == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Failed to parse authentication data"
                };
            }

            // 根据认证步骤处理
            switch (authData.Step)
            {
                case 0: // 初始认证请求
                    return await HandleInitialAuthenticationAsync(authData, socketInfo, cancellationToken);
                
                case 1: // 用户名验证
                    return await HandleUsernameAuthenticationAsync(authData, socketInfo, cancellationToken);
                
                case 2: // 密码验证
                    return await HandlePasswordAuthenticationAsync(authData, socketInfo, cancellationToken);
                
                default:
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Unknown authentication step: {authData.Step}"
                    };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling authentication for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Authentication processing error"
            };
        }
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    public string GenerateRandomString(int length = 30)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);
        
        var result = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            result.Append(chars[bytes[i] % chars.Length]);
        }
        
        return result.ToString();
    }

    /// <summary>
    /// 处理初始认证请求
    /// </summary>
    private async Task<AuthenticationResult> HandleInitialAuthenticationAsync(AuthenticationData authData, SocketInfo socketInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling initial authentication for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 生成随机字符串
            var randomString = GenerateRandomString();
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            _randomStrings[clientKey] = randomString;

            // 构造响应数据
            var responseData = BuildAuthenticationResponse(0x01, randomString);

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling initial authentication");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Failed to process initial authentication"
            };
        }
    }

    /// <summary>
    /// 处理用户名认证
    /// </summary>
    private async Task<AuthenticationResult> HandleUsernameAuthenticationAsync(AuthenticationData authData, SocketInfo socketInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling username authentication for user: {Username}", authData.Username);

            if (string.IsNullOrWhiteSpace(authData.Username))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Username is required"
                };
            }

            // 验证用户是否存在
            if (_dataAccess == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Data access not initialized"
                };
            }

            var sql = "SELECT Id, Username, LoginCode, Password, IsActive FROM Users WHERE LoginCode = @LoginCode AND IsActive = 1";
            var users = await _dataAccess.QueryAsync<UserInfo>(sql, new { LoginCode = authData.Username }, cancellationToken);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User not found or inactive"
                };
            }

            // 构造用户名验证成功响应
            var responseData = BuildAuthenticationResponse(0x02, "Username verified");

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData,
                UserInfo = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling username authentication for user: {Username}", authData.Username);
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Username authentication failed"
            };
        }
    }

    /// <summary>
    /// 处理密码认证
    /// </summary>
    private async Task<AuthenticationResult> HandlePasswordAuthenticationAsync(AuthenticationData authData, SocketInfo socketInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling password authentication for user: {Username}", authData.Username);

            if (string.IsNullOrWhiteSpace(authData.Username) || string.IsNullOrWhiteSpace(authData.Password))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Username and password are required"
                };
            }

            if (_dataAccess == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Data access not initialized"
                };
            }

            // 获取用户信息
            var sql = "SELECT Id, Username, LoginCode, Password, IsActive FROM Users WHERE LoginCode = @LoginCode AND IsActive = 1";
            var users = await _dataAccess.QueryAsync<UserInfo>(sql, new { LoginCode = authData.Username }, cancellationToken);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User not found or inactive"
                };
            }

            // 验证密码
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            if (!_randomStrings.TryGetValue(clientKey, out var randomString))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Authentication session expired"
                };
            }

            // 解密存储的密码
            var decryptedPassword = _cryptoService.DesDecrypt(user.Password, "default_key");
            
            // 验证密码（这里应该根据实际的密码验证逻辑进行调整）
            var expectedPassword = _cryptoService.Md5Hash(decryptedPassword + randomString);
            
            if (!string.Equals(authData.Password, expectedPassword, StringComparison.OrdinalIgnoreCase))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid password"
                };
            }

            // 清理随机字符串
            _randomStrings.Remove(clientKey);

            // 更新用户最后登录信息
            await UpdateUserLastLoginAsync(user.DatabaseId, socketInfo.ClientIp, cancellationToken);

            // 构造认证成功响应
            var responseData = BuildAuthenticationResponse(0x03, "Authentication successful");

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData,
                UserInfo = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling password authentication for user: {Username}", authData.Username);
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Password authentication failed"
            };
        }
    }

    /// <summary>
    /// 解析认证数据
    /// </summary>
    private AuthenticationData? ParseAuthenticationData(byte[] data, int length)
    {
        try
        {
            if (length < 1)
                return null;

            var authData = new AuthenticationData
            {
                Step = data[0]
            };

            int offset = 1;

            // 根据步骤解析不同的数据
            switch (authData.Step)
            {
                case 1: // 用户名
                    if (offset < length)
                    {
                        var usernameLength = Math.Min(50, length - offset);
                        authData.Username = Encoding.UTF8.GetString(data, offset, usernameLength).TrimEnd('\0');
                    }
                    break;

                case 2: // 密码
                    // 解析用户名和密码
                    if (offset + 50 < length)
                    {
                        authData.Username = Encoding.UTF8.GetString(data, offset, 50).TrimEnd('\0');
                        offset += 50;
                        var passwordLength = Math.Min(100, length - offset);
                        authData.Password = Encoding.UTF8.GetString(data, offset, passwordLength).TrimEnd('\0');
                    }
                    break;
            }

            return authData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing authentication data");
            return null;
        }
    }

    /// <summary>
    /// 构建认证响应
    /// </summary>
    private byte[] BuildAuthenticationResponse(byte responseType, string message)
    {
        var messageBytes = Encoding.UTF8.GetBytes(message);
        var response = new byte[3 + messageBytes.Length];
        
        response[0] = 0x01; // 认证命令
        response[1] = 0x01; // 响应
        response[2] = responseType;
        
        Array.Copy(messageBytes, 0, response, 3, messageBytes.Length);
        
        return response;
    }

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    private async Task UpdateUserLastLoginAsync(int userId, string clientIp, CancellationToken cancellationToken)
    {
        try
        {
            if (_dataAccess == null)
                return;

            var sql = "UPDATE Users SET LastLoginTime = @LoginTime, LastLoginIp = @LoginIp WHERE Id = @UserId";
            await _dataAccess.ExecuteSqlAsync(sql, new 
            { 
                LoginTime = DateTime.UtcNow, 
                LoginIp = clientIp, 
                UserId = userId 
            }, false, cancellationToken);

            _logger.LogDebug("Updated last login info for user ID: {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user last login info for user ID: {UserId}", userId);
        }
    }
}

/// <summary>
/// 认证数据
/// </summary>
internal class AuthenticationData
{
    public byte Step { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}
