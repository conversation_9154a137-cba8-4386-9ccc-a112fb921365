using System.Security.Cryptography;
using System.Text;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// 认证处理器实现 - 对应C++中的CSockAuthenDeal
/// </summary>
public class AuthenticationHandler : IAuthenticationHandler
{
    private readonly ILogger<AuthenticationHandler> _logger;
    private readonly ICryptoService _cryptoService;
    
    private IDataAccess? _dataAccess;
    private ISocketHandler? _socketHandler;
    private readonly Dictionary<string, string> _randomStrings = new();

    public AuthenticationHandler(
        ILogger<AuthenticationHandler> logger,
        ICryptoService cryptoService)
    {
        _logger = logger;
        _cryptoService = cryptoService;
    }

    /// <summary>
    /// 初始化认证处理器
    /// </summary>
    public void Initialize(IDataAccess dataAccess, ISocketHandler socketHandler)
    {
        _dataAccess = dataAccess ?? throw new ArgumentNullException(nameof(dataAccess));
        _socketHandler = socketHandler ?? throw new ArgumentNullException(nameof(socketHandler));
    }

    /// <summary>
    /// 处理认证
    /// </summary>
    public async Task<AuthenticationResult> HandleAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            if (data == null || length <= 0)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid authentication data"
                };
            }

            _logger.LogDebug("Processing authentication for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 解析认证数据
            var authData = ParseAuthenticationData(data, length);
            if (authData == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Failed to parse authentication data"
                };
            }

            // 根据认证步骤处理
            switch (authData.Step)
            {
                case 0: // 初始认证请求
                    return await HandleInitialAuthenticationAsync(authData, socketInfo, cancellationToken);
                
                case 1: // 用户名验证
                    return await HandleUsernameAuthenticationAsync(authData, socketInfo, cancellationToken);
                
                case 2: // 密码验证
                    return await HandlePasswordAuthenticationAsync(authData, socketInfo, cancellationToken);
                
                default:
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Unknown authentication step: {authData.Step}"
                    };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling authentication for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Authentication processing error"
            };
        }
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    public string GenerateRandomString(int length = 30)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);
        
        var result = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            result.Append(chars[bytes[i] % chars.Length]);
        }
        
        return result.ToString();
    }

    /// <summary>
    /// 处理初始认证请求
    /// </summary>
    private async Task<AuthenticationResult> HandleInitialAuthenticationAsync(AuthenticationData authData, SocketInfo socketInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling initial authentication for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 生成随机字符串
            var randomString = GenerateRandomString();
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            _randomStrings[clientKey] = randomString;

            // 构造响应数据
            var responseData = BuildAuthenticationResponse(0x01, randomString);

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling initial authentication");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Failed to process initial authentication"
            };
        }
    }

    /// <summary>
    /// 处理用户名认证 - 对应C++中的AuthenDealUser (公共接口)
    /// </summary>
    public async Task<AuthenticationResult> HandleUsernameAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing username authentication for client {ClientIp}:{ClientPort}",
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 解析用户名数据 - 对应C++中的数据解析
            if (data == null || length <= 1)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid username data"
                };
            }

            // 跳过命令字，获取用户名
            var usernameBytes = new byte[length - 1];
            Array.Copy(data, 1, usernameBytes, 0, length - 1);
            var username = Encoding.UTF8.GetString(usernameBytes).TrimEnd('\0');

            if (string.IsNullOrWhiteSpace(username))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Username is required"
                };
            }

            // 验证用户是否存在
            if (_dataAccess == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Data access not initialized"
                };
            }

            var sql = "SELECT Id, Username, LoginCode, Password, IsActive FROM Users WHERE LoginCode = @LoginCode AND IsActive = 1";
            var users = await _dataAccess.QueryAsync<UserInfo>(sql, new { LoginCode = username }, cancellationToken);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                // 构造用户不存在响应 - 对应C++中的AUTH_RESULT_UNKNOWNUSER
                var errorResponse = BuildAuthenticationErrorResponse(CommandDefinitions.AUTH_RESULT_UNKNOWNUSER);
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ResponseData = errorResponse,
                    ErrorMessage = "User not found or inactive"
                };
            }

            // 生成随机字符串用于密码验证
            var randomString = GenerateRandomString();
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            _randomStrings[clientKey] = randomString;

            // 构造用户名验证成功响应 - 对应C++中的RESTYPE_AUTHENTICATION_RAND
            var responseData = BuildAuthenticationRandomResponse(randomString);

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData,
                UserInfo = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling username authentication");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Username authentication processing error"
            };
        }
    }

    /// <summary>
    /// 处理密码认证 - 对应C++中的AuthenDealPassWord (公共接口)
    /// </summary>
    public async Task<AuthenticationResult> HandlePasswordAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing password authentication for client {ClientIp}:{ClientPort}",
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 解析密码数据 - 对应C++中的数据解析
            if (data == null || length <= 1)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid password data"
                };
            }

            // 跳过命令字，解析用户名和密码
            int offset = 1;
            if (offset + 50 >= length)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Insufficient password data"
                };
            }

            var username = Encoding.UTF8.GetString(data, offset, 50).TrimEnd('\0');
            offset += 50;
            var passwordLength = Math.Min(100, length - offset);
            var password = Encoding.UTF8.GetString(data, offset, passwordLength).TrimEnd('\0');

            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Username and password are required"
                };
            }

            if (_dataAccess == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Data access not initialized"
                };
            }

            // 获取用户信息
            var sql = "SELECT Id, Username, LoginCode, Password, IsActive FROM Users WHERE LoginCode = @LoginCode AND IsActive = 1";
            var users = await _dataAccess.QueryAsync<UserInfo>(sql, new { LoginCode = username }, cancellationToken);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                var errorResponse = BuildAuthenticationErrorResponse(CommandDefinitions.AUTH_RESULT_UNKNOWNUSER);
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ResponseData = errorResponse,
                    ErrorMessage = "User not found or inactive"
                };
            }

            // 验证密码
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            if (!_randomStrings.TryGetValue(clientKey, out var randomString))
            {
                var errorResponse = BuildAuthenticationErrorResponse(CommandDefinitions.AUTH_RESULT_PASSWORDERROR);
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ResponseData = errorResponse,
                    ErrorMessage = "Authentication session expired"
                };
            }

            // 解密存储的密码
            var decryptedPassword = _cryptoService.DesDecrypt(user.Password, "default_key");

            // 验证密码（这里应该根据实际的密码验证逻辑进行调整）
            var expectedPassword = _cryptoService.Md5Hash(decryptedPassword + randomString);

            if (!string.Equals(password, expectedPassword, StringComparison.OrdinalIgnoreCase))
            {
                var errorResponse = BuildAuthenticationErrorResponse(CommandDefinitions.AUTH_RESULT_PASSWORDERROR);
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ResponseData = errorResponse,
                    ErrorMessage = "Invalid password"
                };
            }

            // 清理随机字符串
            _randomStrings.Remove(clientKey);

            // 更新用户最后登录信息
            await UpdateUserLastLoginAsync(user.DatabaseId, socketInfo.ClientIp, cancellationToken);

            // 构造认证成功响应 - 对应C++中的AUTH_RESULT_SUCCEED
            var responseData = BuildAuthenticationSuccessResponse();

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData,
                UserInfo = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling password authentication");
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Password authentication processing error"
            };
        }
    }

    /// <summary>
    /// 处理用户名认证 - 内部方法（兼容旧版本）
    /// </summary>
    private async Task<AuthenticationResult> HandleUsernameAuthenticationAsync(AuthenticationData authData, SocketInfo socketInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling username authentication for user: {Username}", authData.Username);

            if (string.IsNullOrWhiteSpace(authData.Username))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Username is required"
                };
            }

            // 验证用户是否存在
            if (_dataAccess == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Data access not initialized"
                };
            }

            var sql = "SELECT Id, Username, LoginCode, Password, IsActive FROM Users WHERE LoginCode = @LoginCode AND IsActive = 1";
            var users = await _dataAccess.QueryAsync<UserInfo>(sql, new { LoginCode = authData.Username }, cancellationToken);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User not found or inactive"
                };
            }

            // 构造用户名验证成功响应
            var responseData = BuildAuthenticationResponse(0x02, "Username verified");

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData,
                UserInfo = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling username authentication for user: {Username}", authData.Username);
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Username authentication failed"
            };
        }
    }

    /// <summary>
    /// 处理密码认证
    /// </summary>
    private async Task<AuthenticationResult> HandlePasswordAuthenticationAsync(AuthenticationData authData, SocketInfo socketInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling password authentication for user: {Username}", authData.Username);

            if (string.IsNullOrWhiteSpace(authData.Username) || string.IsNullOrWhiteSpace(authData.Password))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Username and password are required"
                };
            }

            if (_dataAccess == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Data access not initialized"
                };
            }

            // 获取用户信息
            var sql = "SELECT Id, Username, LoginCode, Password, IsActive FROM Users WHERE LoginCode = @LoginCode AND IsActive = 1";
            var users = await _dataAccess.QueryAsync<UserInfo>(sql, new { LoginCode = authData.Username }, cancellationToken);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "User not found or inactive"
                };
            }

            // 验证密码
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            if (!_randomStrings.TryGetValue(clientKey, out var randomString))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Authentication session expired"
                };
            }

            // 解密存储的密码
            var decryptedPassword = _cryptoService.DesDecrypt(user.Password, "default_key");
            
            // 验证密码（这里应该根据实际的密码验证逻辑进行调整）
            var expectedPassword = _cryptoService.Md5Hash(decryptedPassword + randomString);
            
            if (!string.Equals(authData.Password, expectedPassword, StringComparison.OrdinalIgnoreCase))
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid password"
                };
            }

            // 清理随机字符串
            _randomStrings.Remove(clientKey);

            // 更新用户最后登录信息
            await UpdateUserLastLoginAsync(user.DatabaseId, socketInfo.ClientIp, cancellationToken);

            // 构造认证成功响应
            var responseData = BuildAuthenticationResponse(0x03, "Authentication successful");

            return new AuthenticationResult
            {
                IsSuccess = true,
                ResponseData = responseData,
                UserInfo = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling password authentication for user: {Username}", authData.Username);
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorMessage = "Password authentication failed"
            };
        }
    }

    /// <summary>
    /// 解析认证数据
    /// </summary>
    private AuthenticationData? ParseAuthenticationData(byte[] data, int length)
    {
        try
        {
            if (length < 1)
                return null;

            var authData = new AuthenticationData
            {
                Step = data[0]
            };

            int offset = 1;

            // 根据步骤解析不同的数据
            switch (authData.Step)
            {
                case 1: // 用户名
                    if (offset < length)
                    {
                        var usernameLength = Math.Min(50, length - offset);
                        authData.Username = Encoding.UTF8.GetString(data, offset, usernameLength).TrimEnd('\0');
                    }
                    break;

                case 2: // 密码
                    // 解析用户名和密码
                    if (offset + 50 < length)
                    {
                        authData.Username = Encoding.UTF8.GetString(data, offset, 50).TrimEnd('\0');
                        offset += 50;
                        var passwordLength = Math.Min(100, length - offset);
                        authData.Password = Encoding.UTF8.GetString(data, offset, passwordLength).TrimEnd('\0');
                    }
                    break;
            }

            return authData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing authentication data");
            return null;
        }
    }

    /// <summary>
    /// 构建认证响应
    /// </summary>
    private byte[] BuildAuthenticationResponse(byte responseType, string message)
    {
        var messageBytes = Encoding.UTF8.GetBytes(message);
        var response = new byte[3 + messageBytes.Length];
        
        response[0] = 0x01; // 认证命令
        response[1] = 0x01; // 响应
        response[2] = responseType;
        
        Array.Copy(messageBytes, 0, response, 3, messageBytes.Length);
        
        return response;
    }

    /// <summary>
    /// 构建认证随机码响应 - 对应C++中的RESTYPE_AUTHENTICATION_RAND
    /// </summary>
    private byte[] BuildAuthenticationRandomResponse(string randomString)
    {
        var randomBytes = Encoding.UTF8.GetBytes(randomString);
        var response = new byte[1 + randomBytes.Length];

        response[0] = CommandDefinitions.RESTYPE_AUTHENTICATION_RAND; // 0x70
        Array.Copy(randomBytes, 0, response, 1, randomBytes.Length);

        return response;
    }

    /// <summary>
    /// 构建认证成功响应 - 对应C++中的AUTH_RESULT_SUCCEED
    /// </summary>
    private byte[] BuildAuthenticationSuccessResponse()
    {
        var response = new byte[2];
        response[0] = CommandDefinitions.RESTYPE_AUTHENTICATION_RESULT; // 0x71
        response[1] = CommandDefinitions.AUTH_RESULT_SUCCEED; // 0x00

        return response;
    }

    /// <summary>
    /// 构建认证错误响应 - 对应C++中的各种AUTH_RESULT_*错误码
    /// </summary>
    private byte[] BuildAuthenticationErrorResponse(byte errorCode)
    {
        var response = new byte[2];
        response[0] = CommandDefinitions.RESTYPE_AUTHENTICATION_RESULT; // 0x71
        response[1] = errorCode; // 错误码

        return response;
    }

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    private async Task UpdateUserLastLoginAsync(int userId, string clientIp, CancellationToken cancellationToken)
    {
        try
        {
            if (_dataAccess == null)
                return;

            var sql = "UPDATE Users SET LastLoginTime = @LoginTime, LastLoginIp = @LoginIp WHERE Id = @UserId";
            await _dataAccess.ExecuteSqlAsync(sql, new
            {
                LoginTime = DateTime.UtcNow,
                LoginIp = clientIp,
                UserId = userId
            }, false, cancellationToken);

            _logger.LogDebug("Updated last login info for user ID: {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user last login info for user ID: {UserId}", userId);
        }
    }
}

/// <summary>
/// 认证数据
/// </summary>
internal class AuthenticationData
{
    public byte Step { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}
