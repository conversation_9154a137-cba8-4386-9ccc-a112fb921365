using System.Data;
using DtDrvApp.Core.Configuration;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using DtDrvApp.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SqlSugar;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// SqlSugar 数据访问服务实现
/// </summary>
public class SqlSugarDataAccessService : IDataAccess
{
    private readonly DtDbContext _context;
    private readonly ILogger<SqlSugarDataAccessService> _logger;
    private readonly IOptions<DatabaseOptions> _databaseOptions;
    private DatabaseConnectionInfo? _connectionInfo;

    public SqlSugarDataAccessService(
        DtDbContext context,
        ILogger<SqlSugarDataAccessService> logger,
        IOptions<DatabaseOptions> databaseOptions)
    {
        _context = context;
        _logger = logger;
        _databaseOptions = databaseOptions;
    }

    /// <summary>
    /// 设置数据库连接参数
    /// </summary>
    public void SetConnectionInfo(DatabaseConnectionInfo connectionInfo)
    {
        _connectionInfo = connectionInfo ?? throw new ArgumentNullException(nameof(connectionInfo));
        _logger.LogDebug("Database connection info updated for server: {Server}, database: {Database}", 
            connectionInfo.ServerAddress, connectionInfo.DatabaseName);
    }

    /// <summary>
    /// 执行SQL语句
    /// </summary>
    public async Task<int> ExecuteSqlAsync(string sql, object? parameters = null, bool displayLog = true, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(sql))
            throw new ArgumentException("SQL cannot be null or empty", nameof(sql));

        try
        {
            if (displayLog && _databaseOptions.Value.DisplaySql)
            {
                _logger.LogInformation("Executing SQL: {Sql}", sql);
                if (parameters != null)
                {
                    _logger.LogDebug("Parameters: {@Parameters}", parameters);
                }
            }

            var result = await _context.ExecuteSqlAsync(sql, parameters);
            
            if (displayLog)
            {
                _logger.LogDebug("SQL execution completed. Rows affected: {RowsAffected}", result);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing SQL: {Sql}", sql);
            throw;
        }
    }

    /// <summary>
    /// 查询数据
    /// </summary>
    public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(sql))
            throw new ArgumentException("SQL cannot be null or empty", nameof(sql));

        try
        {
            if (_databaseOptions.Value.DisplaySql)
            {
                _logger.LogInformation("Executing query: {Sql}", sql);
                if (parameters != null)
                {
                    _logger.LogDebug("Parameters: {@Parameters}", parameters);
                }
            }

            var result = await _context.QuerySqlAsync<T>(sql, parameters);

            _logger.LogDebug("Query completed. Records returned: {Count}", result.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing query: {Sql}", sql);
            throw;
        }
    }

    /// <summary>
    /// 查询单个对象
    /// </summary>
    public async Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default)
    {
        var results = await QueryAsync<T>(sql, parameters, cancellationToken);
        return results.FirstOrDefault();
    }

    /// <summary>
    /// 查询标量值
    /// </summary>
    public async Task<object?> QueryScalarAsync(string sql, object? parameters = null, bool displayLog = true, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(sql))
            throw new ArgumentException("SQL cannot be null or empty", nameof(sql));

        try
        {
            if (displayLog && _databaseOptions.Value.DisplaySql)
            {
                _logger.LogInformation("Executing scalar query: {Sql}", sql);
                if (parameters != null)
                {
                    _logger.LogDebug("Parameters: {@Parameters}", parameters);
                }
            }

            var result = await _context.Database.Ado.GetScalarAsync(sql, parameters);
            
            if (displayLog)
            {
                _logger.LogDebug("Scalar query completed. Result: {Result}", result);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing scalar query: {Sql}", sql);
            throw;
        }
    }

    /// <summary>
    /// 开始事务
    /// </summary>
    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _context.BeginTransaction();
            _logger.LogDebug("Transaction started");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting transaction");
            throw;
        }
    }

    /// <summary>
    /// 提交事务
    /// </summary>
    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _context.CommitTransaction();
            _logger.LogDebug("Transaction committed");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error committing transaction");
            throw;
        }
    }

    /// <summary>
    /// 回滚事务
    /// </summary>
    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _context.RollbackTransaction();
            _logger.LogDebug("Transaction rolled back");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rolling back transaction");
            throw;
        }
    }

    /// <summary>
    /// 测试数据库连接
    /// </summary>
    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _context.Database.Ado.GetScalarAsync("SELECT 1");
            _logger.LogDebug("Database connection test successful");
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Database connection test failed");
            return false;
        }
    }

    /// <summary>
    /// 获取数据库连接信息
    /// </summary>
    public DatabaseConnectionInfo? GetConnectionInfo()
    {
        return _connectionInfo;
    }

    /// <summary>
    /// 检查数据库连接是否可用
    /// </summary>
    public bool IsConnectionAvailable()
    {
        return _connectionInfo != null;
    }

    /// <summary>
    /// 获取连接超时时间
    /// </summary>
    public int GetConnectionTimeout()
    {
        return _connectionInfo?.ConnectionTimeout ?? _databaseOptions.Value.ConnectionTimeout;
    }

    /// <summary>
    /// 获取命令超时时间
    /// </summary>
    public int GetCommandTimeout()
    {
        return _connectionInfo?.CommandTimeout ?? _databaseOptions.Value.CommandTimeout;
    }

    /// <summary>
    /// 批量导入数据文件
    /// </summary>
    public async Task BulkCopyDataFileAsync(string filename, string tablename, bool deleteMode = true, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting bulk copy from file: {Filename} to table: {TableName}", filename, tablename);

            // 这里需要根据具体需求实现批量导入逻辑
            // SqlSugar 支持批量插入，但文件导入需要自定义实现
            throw new NotImplementedException("BulkCopyDataFileAsync method needs to be implemented based on specific requirements");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk copy operation");
            throw;
        }
    }

    /// <summary>
    /// 检查数据库连接是否正常
    /// </summary>
    public async Task<bool> IsConnectionNormalAsync(CancellationToken cancellationToken = default)
    {
        return await TestConnectionAsync(cancellationToken);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _context?.Dispose();
    }
}
