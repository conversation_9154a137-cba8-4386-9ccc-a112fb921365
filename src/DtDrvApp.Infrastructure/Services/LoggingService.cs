using DtDrvApp.Core.Extensions;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// 日志服务实现 - 对应C++中的PPM_Log_Msg
/// </summary>
public class LoggingService
{
    private readonly ILogger<LoggingService> _logger;
    private readonly ConcurrentDictionary<string, Stopwatch> _performanceTimers = new();
    private readonly ConcurrentDictionary<string, long> _counters = new();

    public LoggingService(ILogger<LoggingService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 开始性能计时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>计时器键</returns>
    public string StartPerformanceTimer(string operationName)
    {
        var timerKey = $"{operationName}_{Guid.NewGuid():N}";
        var stopwatch = Stopwatch.StartNew();
        _performanceTimers[timerKey] = stopwatch;
        
        _logger.LogDebug("Performance timer started for operation: {OperationName}", operationName);
        return timerKey;
    }

    /// <summary>
    /// 停止性能计时并记录日志
    /// </summary>
    /// <param name="timerKey">计时器键</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="context">上下文信息</param>
    public void StopPerformanceTimer(string timerKey, string operationName, string? context = null)
    {
        if (_performanceTimers.TryRemove(timerKey, out var stopwatch))
        {
            stopwatch.Stop();
            _logger.LogPerformanceMetric(operationName, stopwatch.ElapsedMilliseconds, "ms", context);
        }
    }

    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    /// <param name="increment">增量</param>
    /// <returns>当前计数值</returns>
    public long IncrementCounter(string counterName, long increment = 1)
    {
        var newValue = _counters.AddOrUpdate(counterName, increment, (key, oldValue) => oldValue + increment);
        
        if (newValue % 1000 == 0) // 每1000次记录一次日志
        {
            _logger.LogDebug("Counter {CounterName} reached {Value}", counterName, newValue);
        }
        
        return newValue;
    }

    /// <summary>
    /// 获取计数器值
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    /// <returns>计数值</returns>
    public long GetCounterValue(string counterName)
    {
        return _counters.GetValueOrDefault(counterName, 0);
    }

    /// <summary>
    /// 重置计数器
    /// </summary>
    /// <param name="counterName">计数器名称</param>
    public void ResetCounter(string counterName)
    {
        _counters.TryRemove(counterName, out _);
        _logger.LogDebug("Counter {CounterName} reset", counterName);
    }

    /// <summary>
    /// 记录结构化日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="eventId">事件ID</param>
    /// <param name="message">消息模板</param>
    /// <param name="args">参数</param>
    public void LogStructured(LogLevel level, EventId eventId, string message, params object[] args)
    {
        _logger.Log(level, eventId, message, args);
    }

    /// <summary>
    /// 记录异常日志
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文</param>
    /// <param name="additionalData">附加数据</param>
    public void LogException(Exception exception, string context, object? additionalData = null)
    {
        _logger.LogError(exception, 
            "Exception occurred in context {Context}: {ExceptionType} - {ExceptionMessage} {@AdditionalData}",
            context, exception.GetType().Name, exception.Message, additionalData);
    }

    /// <summary>
    /// 记录业务事件
    /// </summary>
    /// <param name="eventName">事件名称</param>
    /// <param name="username">用户名</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="eventData">事件数据</param>
    public void LogBusinessEvent(string eventName, string username, string clientIp, object? eventData = null)
    {
        _logger.LogInformation(
            "Business event {EventName} by user {Username} from {ClientIp}: {@EventData}",
            eventName, username, clientIp, eventData);
    }

    /// <summary>
    /// 记录安全事件
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="username">用户名</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="success">是否成功</param>
    /// <param name="details">详细信息</param>
    public void LogSecurityEvent(string eventType, string username, string clientIp, 
        bool success, string? details = null)
    {
        var level = success ? LogLevel.Information : LogLevel.Warning;
        var status = success ? "successful" : "failed";
        
        _logger.Log(level, 
            "Security event {EventType} {Status} for user {Username} from {ClientIp}: {Details}",
            eventType, status, username, clientIp, details ?? "N/A");
    }

    /// <summary>
    /// 记录系统健康状态
    /// </summary>
    /// <param name="component">组件名称</param>
    /// <param name="status">状态</param>
    /// <param name="metrics">指标</param>
    public void LogHealthStatus(string component, string status, Dictionary<string, object>? metrics = null)
    {
        _logger.LogInformation(
            "Health check for component {Component}: {Status} {@Metrics}",
            component, status, metrics);
    }

    /// <summary>
    /// 记录配置变更审计
    /// </summary>
    /// <param name="configSection">配置节</param>
    /// <param name="configKey">配置键</param>
    /// <param name="oldValue">旧值</param>
    /// <param name="newValue">新值</param>
    /// <param name="username">操作用户</param>
    /// <param name="clientIp">客户端IP</param>
    public void LogConfigurationAudit(string configSection, string configKey, 
        string? oldValue, string? newValue, string username, string clientIp)
    {
        _logger.LogInformation(
            "Configuration audit: {ConfigSection}.{ConfigKey} changed from '{OldValue}' to '{NewValue}' " +
            "by user {Username} from {ClientIp}",
            configSection, configKey, oldValue ?? "null", newValue ?? "null", username, clientIp);
    }

    /// <summary>
    /// 记录数据访问审计
    /// </summary>
    /// <param name="operation">操作类型</param>
    /// <param name="tableName">表名</param>
    /// <param name="recordCount">记录数</param>
    /// <param name="username">用户名</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="duration">持续时间</param>
    public void LogDataAccessAudit(string operation, string tableName, int recordCount, 
        string username, string clientIp, TimeSpan duration)
    {
        _logger.LogInformation(
            "Data access audit: {Operation} on table {TableName} affected {RecordCount} records " +
            "by user {Username} from {ClientIp} in {Duration}ms",
            operation, tableName, recordCount, username, clientIp, duration.TotalMilliseconds);
    }

    /// <summary>
    /// 记录网络流量统计
    /// </summary>
    /// <param name="direction">方向（Inbound/Outbound）</param>
    /// <param name="bytes">字节数</param>
    /// <param name="clientInfo">客户端信息</param>
    public void LogNetworkTraffic(string direction, long bytes, string clientInfo)
    {
        IncrementCounter($"NetworkTraffic_{direction}_Bytes", bytes);
        IncrementCounter($"NetworkTraffic_{direction}_Packets", 1);
        
        _logger.LogDebug(
            "Network traffic {Direction}: {Bytes} bytes for client {ClientInfo}",
            direction, bytes, clientInfo);
    }

    /// <summary>
    /// 记录缓存操作
    /// </summary>
    /// <param name="operation">操作类型</param>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="hit">是否命中</param>
    /// <param name="duration">持续时间</param>
    public void LogCacheOperation(string operation, string cacheKey, bool hit, TimeSpan duration)
    {
        var hitStatus = hit ? "hit" : "miss";
        IncrementCounter($"Cache_{operation}_{hitStatus}");
        
        _logger.LogDebug(
            "Cache {Operation} {HitStatus} for key {CacheKey} in {Duration}ms",
            operation, hitStatus, cacheKey, duration.TotalMilliseconds);
    }

    /// <summary>
    /// 获取所有计数器统计
    /// </summary>
    /// <returns>计数器统计字典</returns>
    public Dictionary<string, long> GetCounterStatistics()
    {
        return new Dictionary<string, long>(_counters);
    }

    /// <summary>
    /// 清理过期的性能计时器
    /// </summary>
    /// <param name="maxAge">最大存活时间</param>
    public void CleanupExpiredTimers(TimeSpan maxAge)
    {
        var expiredKeys = new List<string>();
        var cutoffTime = DateTime.UtcNow - maxAge;

        foreach (var kvp in _performanceTimers)
        {
            // 这里简化处理，实际应该记录计时器创建时间
            if (kvp.Value.Elapsed > maxAge)
            {
                expiredKeys.Add(kvp.Key);
            }
        }

        foreach (var key in expiredKeys)
        {
            if (_performanceTimers.TryRemove(key, out var timer))
            {
                timer.Stop();
                _logger.LogWarning("Expired performance timer removed: {TimerKey}", key);
            }
        }
    }

    /// <summary>
    /// 记录应用程序生命周期事件
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="details">详细信息</param>
    public void LogApplicationLifecycle(string eventType, string? details = null)
    {
        _logger.LogInformation(
            "Application lifecycle event: {EventType} - {Details}",
            eventType, details ?? "N/A");
    }
}
