using System.Collections.Concurrent;
using System.Text;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// Socket处理器实现 - 对应C++中的CDtSockServer
/// </summary>
public class SocketHandler : ISocketHandler
{
    private readonly ILogger<SocketHandler> _logger;
    private readonly ICommandDispatcher _commandDispatcher;
    private readonly IAuthenticationHandler _authenticationHandler;
    private readonly IDataAccess _dataAccess;
    
    private readonly ConcurrentDictionary<string, byte[]> _sendBuffers = new();
    private const int SEND_BUF_MAXSIZE = 65536;

    public SocketHandler(
        ILogger<SocketHandler> logger,
        ICommandDispatcher commandDispatcher,
        IAuthenticationHandler authenticationHandler,
        IDataAccess dataAccess)
    {
        _logger = logger;
        _commandDispatcher = commandDispatcher;
        _authenticationHandler = authenticationHandler;
        _dataAccess = dataAccess;
        
        // 初始化认证处理器
        _authenticationHandler.Initialize(_dataAccess, this);
    }

    /// <summary>
    /// 处理数据 - 对应C++中的DealData
    /// </summary>
    public async Task<bool> HandleDataAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            if (data == null || length <= 0)
            {
                _logger.LogWarning("Received empty data from client {ClientIp}:{ClientPort}",
                    socketInfo.ClientIp, socketInfo.ClientPort);
                return false;
            }

            _logger.LogDebug("Received {Length} bytes from client {ClientIp}:{ClientPort}",
                length, socketInfo.ClientIp, socketInfo.ClientPort);

            // 直接处理服务器Socket数据，让命令分发器决定如何处理
            return await DealServerSocketAsync(data, length, socketInfo, 0, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling data from client {ClientIp}:{ClientPort}",
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 开始处理连接 - 对应C++中的BeginDeal
    /// </summary>
    public async Task BeginHandleAsync(SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Begin handling connection from {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 初始化发送缓冲区
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            _sendBuffers[clientKey] = new byte[SEND_BUF_MAXSIZE];

            // 更新用户登录信息（如果需要）
            await UpdateUserLoginInfoAsync(socketInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error beginning handle for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
        }
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    public async Task<int> SendDataAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            if (data == null || length <= 0)
            {
                return 0;
            }

            // 这里应该通过网络连接发送数据
            // 由于我们在这个层面没有直接的网络连接引用，
            // 实际的发送操作应该由上层的网络服务处理
            
            _logger.LogDebug("Sending {Length} bytes to client {ClientIp}:{ClientPort}", 
                length, socketInfo.ClientIp, socketInfo.ClientPort);

            // 返回发送的字节数
            return length;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending data to client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return -1;
        }
    }





    /// <summary>
    /// 处理服务器Socket数据 - 对应C++中的DealServerSock
    /// </summary>
    private async Task<bool> DealServerSocketAsync(byte[] data, int length, SocketInfo socketInfo, int offset, CancellationToken cancellationToken)
    {
        try
        {
            if (offset >= length)
            {
                return false;
            }

            // 获取命令字
            byte commandId = data[offset];

            _logger.LogDebug("Processing command {CommandId:X2} from client {ClientIp}:{ClientPort}",
                commandId, socketInfo.ClientIp, socketInfo.ClientPort);

            // 对于非认证命令，检查认证状态 - 对应C++中的认证检查逻辑
            if (!IsAuthenticationCommand(commandId) && !socketInfo.IsAuthenticated)
            {
                _logger.LogWarning("Client {ClientIp}:{ClientPort} attempted to execute command {CommandId:X2} without authentication",
                    socketInfo.ClientIp, socketInfo.ClientPort, commandId);

                // 发送认证错误响应
                var errorResponse = BuildErrorResponse(CommandDefinitions.RESTYPE_SEARCHERROR, "Authentication required");
                await SendDataAsync(errorResponse, errorResponse.Length, socketInfo, cancellationToken);
                return false;
            }

            // 创建命令请求
            var request = new CommandRequest
            {
                CommandId = commandId,
                SubCommandId = offset + 1 < length ? data[offset + 1] : (byte)0,
                Data = data,
                DataLength = length,
                SocketInfo = socketInfo,
                RequestTime = DateTime.UtcNow
            };

            // 分发命令
            var response = await _commandDispatcher.DispatchAsync(commandId, request, cancellationToken);

            // 发送响应
            if (response.Data.Length > 0)
            {
                await SendDataAsync(response.Data, response.Data.Length, socketInfo, cancellationToken);
            }

            return response.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dealing with server socket data from client {ClientIp}:{ClientPort}",
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 处理认证失败 - 对应C++中的DealAuthencateFialed
    /// </summary>
    private async Task<bool> DealAuthenticationFailedAsync(SocketInfo socketInfo)
    {
        try
        {
            _logger.LogWarning("Authentication failed for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 记录登录失败日志
            await WriteUserLoginFailLogAsync(socketInfo.UserInfo?.Username ?? "unknown", "Authentication failed");

            // 可以在这里发送认证失败响应
            var errorResponse = new byte[] { 0xFF, 0xFF, 0x00 }; // 示例错误响应
            await SendDataAsync(errorResponse, errorResponse.Length, socketInfo);

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dealing with authentication failure for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 更新用户登录信息
    /// </summary>
    private async Task UpdateUserLoginInfoAsync(SocketInfo socketInfo)
    {
        try
        {
            // 这里可以记录用户连接信息
            _logger.LogDebug("Updating user login info for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user login info for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
        }
    }

    /// <summary>
    /// 写入用户登录失败日志
    /// </summary>
    private async Task WriteUserLoginFailLogAsync(string username, string eventDescription)
    {
        try
        {
            _logger.LogWarning("User login failed - Username: {Username}, Event: {Event}", username, eventDescription);
            
            // 这里可以写入数据库日志
            // await _dataAccess.ExecuteSqlAsync("INSERT INTO UserLoginLogs ...", new { username, eventDescription });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error writing user login fail log for user: {Username}", username);
        }
    }

    /// <summary>
    /// 检查是否为认证命令 - 对应C++中的认证命令检查
    /// </summary>
    private bool IsAuthenticationCommand(byte commandId)
    {
        return commandId == CommandDefinitions.CMD1_AUTHENTICATION ||
               commandId == CommandDefinitions.REQTYPE_AUTHENTICATION_USER ||
               commandId == CommandDefinitions.REQTYPE_AUTHENTICATION_PASSWORD ||
               commandId == CommandDefinitions.REQTYPE_AUTHENTICATION_USER2 ||
               commandId == CommandDefinitions.REQTYPE_AUTHENTICATION_PASSWORD2;
    }

    /// <summary>
    /// 构建错误响应
    /// </summary>
    private byte[] BuildErrorResponse(byte responseType, string message)
    {
        var messageBytes = Encoding.UTF8.GetBytes(message);
        var response = new byte[3 + messageBytes.Length];

        response[0] = responseType;
        response[1] = (byte)(messageBytes.Length & 0xFF);
        response[2] = (byte)((messageBytes.Length >> 8) & 0xFF);

        Array.Copy(messageBytes, 0, response, 3, messageBytes.Length);

        return response;
    }
}
