using System.Collections.Concurrent;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// Socket处理器实现 - 对应C++中的CDtSockServer
/// </summary>
public class SocketHandler : ISocketHandler
{
    private readonly ILogger<SocketHandler> _logger;
    private readonly ICommandDispatcher _commandDispatcher;
    private readonly IAuthenticationHandler _authenticationHandler;
    private readonly IDataAccess _dataAccess;
    
    private readonly ConcurrentDictionary<string, byte[]> _sendBuffers = new();
    private const int SEND_BUF_MAXSIZE = 65536;

    public SocketHandler(
        ILogger<SocketHandler> logger,
        ICommandDispatcher commandDispatcher,
        IAuthenticationHandler authenticationHandler,
        IDataAccess dataAccess)
    {
        _logger = logger;
        _commandDispatcher = commandDispatcher;
        _authenticationHandler = authenticationHandler;
        _dataAccess = dataAccess;
        
        // 初始化认证处理器
        _authenticationHandler.Initialize(_dataAccess, this);
    }

    /// <summary>
    /// 处理数据 - 对应C++中的DealData
    /// </summary>
    public async Task<bool> HandleDataAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            if (data == null || length <= 0)
            {
                _logger.LogWarning("Received empty data from client {ClientIp}:{ClientPort}", 
                    socketInfo.ClientIp, socketInfo.ClientPort);
                return false;
            }

            _logger.LogDebug("Received {Length} bytes from client {ClientIp}:{ClientPort}", 
                length, socketInfo.ClientIp, socketInfo.ClientPort);

            int offset = 0;
            
            // 处理Socket数据
            return await DealSocketAsync(data, length, socketInfo, offset, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling data from client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 开始处理连接 - 对应C++中的BeginDeal
    /// </summary>
    public async Task BeginHandleAsync(SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Begin handling connection from {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 初始化发送缓冲区
            var clientKey = $"{socketInfo.ClientIp}:{socketInfo.ClientPort}";
            _sendBuffers[clientKey] = new byte[SEND_BUF_MAXSIZE];

            // 更新用户登录信息（如果需要）
            await UpdateUserLoginInfoAsync(socketInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error beginning handle for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
        }
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    public async Task<int> SendDataAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            if (data == null || length <= 0)
            {
                return 0;
            }

            // 这里应该通过网络连接发送数据
            // 由于我们在这个层面没有直接的网络连接引用，
            // 实际的发送操作应该由上层的网络服务处理
            
            _logger.LogDebug("Sending {Length} bytes to client {ClientIp}:{ClientPort}", 
                length, socketInfo.ClientIp, socketInfo.ClientPort);

            // 返回发送的字节数
            return length;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending data to client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return -1;
        }
    }

    /// <summary>
    /// 处理Socket数据 - 对应C++中的DealSock
    /// </summary>
    private async Task<bool> DealSocketAsync(byte[] data, int length, SocketInfo socketInfo, int offset, CancellationToken cancellationToken)
    {
        try
        {
            // 检查是否已认证
            if (!socketInfo.IsAuthenticated)
            {
                // 处理认证
                var authResult = await _authenticationHandler.HandleAuthenticationAsync(data, length, socketInfo, cancellationToken);
                
                if (authResult.IsSuccess)
                {
                    socketInfo.IsAuthenticated = true;
                    socketInfo.UserInfo = authResult.UserInfo;
                    
                    if (authResult.ResponseData != null)
                    {
                        await SendDataAsync(authResult.ResponseData, authResult.ResponseData.Length, socketInfo, cancellationToken);
                    }
                    
                    return true;
                }
                else
                {
                    _logger.LogWarning("Authentication failed for client {ClientIp}:{ClientPort}: {Error}", 
                        socketInfo.ClientIp, socketInfo.ClientPort, authResult.ErrorMessage);
                    
                    await DealAuthenticationFailedAsync(socketInfo);
                    return false;
                }
            }

            // 已认证，处理业务数据
            return await DealAuthenticatedDataAsync(data, length, socketInfo, offset, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dealing with socket data from client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 处理已认证的数据 - 对应C++中的DealAuthencateSuccess
    /// </summary>
    private async Task<bool> DealAuthenticatedDataAsync(byte[] data, int length, SocketInfo socketInfo, int offset, CancellationToken cancellationToken)
    {
        try
        {
            if (offset >= length)
            {
                _logger.LogWarning("Invalid data offset from client {ClientIp}:{ClientPort}", 
                    socketInfo.ClientIp, socketInfo.ClientPort);
                return false;
            }

            // 获取数据库ID
            if (offset + 4 > length)
            {
                _logger.LogWarning("Insufficient data for database ID from client {ClientIp}:{ClientPort}", 
                    socketInfo.ClientIp, socketInfo.ClientPort);
                return false;
            }

            int databaseId = BitConverter.ToInt32(data, offset);
            offset += 4;

            // 设置数据库连接
            if (databaseId > 0)
            {
                // 这里应该从数据库信息管理器获取连接信息
                // 并设置到数据访问层
                _logger.LogDebug("Using database ID: {DatabaseId} for client {ClientIp}:{ClientPort}", 
                    databaseId, socketInfo.ClientIp, socketInfo.ClientPort);
            }

            // 处理服务器Socket数据
            return await DealServerSocketAsync(data, length, socketInfo, offset, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dealing with authenticated data from client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 处理服务器Socket数据 - 对应C++中的DealServerSock
    /// </summary>
    private async Task<bool> DealServerSocketAsync(byte[] data, int length, SocketInfo socketInfo, int offset, CancellationToken cancellationToken)
    {
        try
        {
            if (offset >= length)
            {
                return false;
            }

            // 获取命令字
            byte commandId = data[offset];
            
            // 创建命令请求
            var request = new CommandRequest
            {
                CommandId = commandId,
                SubCommandId = offset + 1 < length ? data[offset + 1] : (byte)0,
                Data = data,
                DataLength = length,
                SocketInfo = socketInfo,
                RequestTime = DateTime.UtcNow
            };

            // 分发命令
            var response = await _commandDispatcher.DispatchAsync(commandId, request, cancellationToken);

            // 发送响应
            if (response.Data.Length > 0)
            {
                await SendDataAsync(response.Data, response.Data.Length, socketInfo, cancellationToken);
            }

            return response.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dealing with server socket data from client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 处理认证失败 - 对应C++中的DealAuthencateFialed
    /// </summary>
    private async Task<bool> DealAuthenticationFailedAsync(SocketInfo socketInfo)
    {
        try
        {
            _logger.LogWarning("Authentication failed for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);

            // 记录登录失败日志
            await WriteUserLoginFailLogAsync(socketInfo.UserInfo?.Username ?? "unknown", "Authentication failed");

            // 可以在这里发送认证失败响应
            var errorResponse = new byte[] { 0xFF, 0xFF, 0x00 }; // 示例错误响应
            await SendDataAsync(errorResponse, errorResponse.Length, socketInfo);

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dealing with authentication failure for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
            return false;
        }
    }

    /// <summary>
    /// 更新用户登录信息
    /// </summary>
    private async Task UpdateUserLoginInfoAsync(SocketInfo socketInfo)
    {
        try
        {
            // 这里可以记录用户连接信息
            _logger.LogDebug("Updating user login info for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user login info for client {ClientIp}:{ClientPort}", 
                socketInfo.ClientIp, socketInfo.ClientPort);
        }
    }

    /// <summary>
    /// 写入用户登录失败日志
    /// </summary>
    private async Task WriteUserLoginFailLogAsync(string username, string eventDescription)
    {
        try
        {
            _logger.LogWarning("User login failed - Username: {Username}, Event: {Event}", username, eventDescription);
            
            // 这里可以写入数据库日志
            // await _dataAccess.ExecuteSqlAsync("INSERT INTO UserLoginLogs ...", new { username, eventDescription });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error writing user login fail log for user: {Username}", username);
        }
    }
}
