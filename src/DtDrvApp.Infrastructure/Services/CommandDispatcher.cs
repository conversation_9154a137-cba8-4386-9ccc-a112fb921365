using System.Collections.Concurrent;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// 命令分发器实现 - 对应C++中的SwitchSock
/// </summary>
public class CommandDispatcher : ICommandDispatcher
{
    private readonly ILogger<CommandDispatcher> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ConcurrentDictionary<byte, Type> _handlers = new();

    public CommandDispatcher(
        ILogger<CommandDispatcher> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        RegisterBuiltInHandlers();
    }

    /// <summary>
    /// 分发命令
    /// </summary>
    public async Task<CommandResponse> DispatchAsync(byte commandId, CommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Dispatching command {CommandId:X2} from client {ClientIp}:{ClientPort}", 
                commandId, request.SocketInfo.ClientIp, request.SocketInfo.ClientPort);

            if (!_handlers.TryGetValue(commandId, out var handlerType))
            {
                _logger.LogWarning("No handler found for command {CommandId:X2}", commandId);
                return new CommandResponse
                {
                    Success = false,
                    ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                    ErrorMessage = $"Command {commandId:X2} not supported"
                };
            }

            // 从服务容器获取处理器实例
            var handler = _serviceProvider.GetService(handlerType) as ICommandHandler;
            if (handler == null)
            {
                _logger.LogError("Failed to create handler instance for command {CommandId:X2}", commandId);
                return new CommandResponse
                {
                    Success = false,
                    ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                    ErrorMessage = "Handler creation failed"
                };
            }

            // 执行命令处理
            var response = await handler.HandleAsync(request, cancellationToken);
            
            _logger.LogDebug("Command {CommandId:X2} processed, success: {Success}", commandId, response.Success);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dispatching command {CommandId:X2}", commandId);
            return new CommandResponse
            {
                Success = false,
                ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                ErrorMessage = "Command processing error"
            };
        }
    }

    /// <summary>
    /// 注册命令处理器
    /// </summary>
    public void RegisterHandler(ICommandHandler handler)
    {
        if (handler == null)
            throw new ArgumentNullException(nameof(handler));

        var commandId = handler.CommandId;
        var handlerType = handler.GetType();

        _handlers.AddOrUpdate(commandId, handlerType, (key, oldValue) =>
        {
            _logger.LogWarning("Replacing existing handler for command {CommandId:X2}: {OldType} -> {NewType}", 
                commandId, oldValue.Name, handlerType.Name);
            return handlerType;
        });

        _logger.LogDebug("Registered handler {HandlerType} for command {CommandId:X2}", 
            handlerType.Name, commandId);
    }

    /// <summary>
    /// 获取所有已注册的命令处理器
    /// </summary>
    public IEnumerable<ICommandHandler> GetRegisteredHandlers()
    {
        var handlers = new List<ICommandHandler>();
        
        foreach (var handlerType in _handlers.Values.Distinct())
        {
            try
            {
                var handler = _serviceProvider.GetService(handlerType) as ICommandHandler;
                if (handler != null)
                {
                    handlers.Add(handler);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to create handler instance for type {HandlerType}", handlerType.Name);
            }
        }

        return handlers;
    }

    /// <summary>
    /// 注册内置处理器
    /// </summary>
    private void RegisterBuiltInHandlers()
    {
        // 注册所有命令处理器类型
        _handlers[CommandDefinitions.CMD1_AUTHENTICATION] = typeof(AuthenticationCommandHandler);

        // 注册认证子命令处理器 - 对应C++中的pfunc_AuthenDealData
        _handlers[CommandDefinitions.REQTYPE_AUTHENTICATION_USER] = typeof(AuthenticationUserCommandHandler);
        _handlers[CommandDefinitions.REQTYPE_AUTHENTICATION_PASSWORD] = typeof(AuthenticationPasswordCommandHandler);
        _handlers[CommandDefinitions.REQTYPE_AUTHENTICATION_USER2] = typeof(AuthenticationUser2CommandHandler);
        _handlers[CommandDefinitions.REQTYPE_AUTHENTICATION_PASSWORD2] = typeof(AuthenticationPassword2CommandHandler);

        _handlers[CommandDefinitions.CMD1_TESTDATAMANAGEMENT] = typeof(TestDataManagementCommandHandler);
        _handlers[CommandDefinitions.CMD1_CONFIG_MNG] = typeof(ConfigManagementCommandHandler);
        _handlers[CommandDefinitions.CMD1_DBMANAGEMENT] = typeof(DatabaseManagementCommandHandler);
        _handlers[CommandDefinitions.CMD1_SEARCH] = typeof(SearchCommandHandler);
        _handlers[CommandDefinitions.CMD1_SEARCH_STATISTIC] = typeof(StatisticCommandHandler);
        _handlers[CommandDefinitions.CMD1_USER_MNG] = typeof(UserManagementCommandHandler);
        _handlers[CommandDefinitions.CMD1_COMMUNITY_MNG] = typeof(CommunityManagementCommandHandler);
        _handlers[CommandDefinitions.CMD1_DIYSEARCH] = typeof(DIYSearchCommandHandler);

        _logger.LogInformation("Registered {Count} built-in command handlers", _handlers.Count);
    }
}

/// <summary>
/// 认证命令处理器
/// </summary>
public class AuthenticationCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_AUTHENTICATION;
    
    private readonly ILogger<AuthenticationCommandHandler> _logger;
    private readonly IAuthenticationHandler _authenticationHandler;

    public AuthenticationCommandHandler(
        ILogger<AuthenticationCommandHandler> logger,
        IAuthenticationHandler authenticationHandler)
    {
        _logger = logger;
        _authenticationHandler = authenticationHandler;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing authentication command from client {ClientIp}:{ClientPort}",
                request.SocketInfo.ClientIp, request.SocketInfo.ClientPort);

            var authResult = await _authenticationHandler.HandleAuthenticationAsync(
                request.Data, request.DataLength, request.SocketInfo, cancellationToken);

            // 如果认证成功，更新 SocketInfo 状态
            if (authResult.IsSuccess && authResult.UserInfo != null)
            {
                request.SocketInfo.IsAuthenticated = true;
                request.SocketInfo.UserInfo = authResult.UserInfo;

                _logger.LogInformation("Client {ClientIp}:{ClientPort} authenticated successfully as user {Username}",
                    request.SocketInfo.ClientIp, request.SocketInfo.ClientPort, authResult.UserInfo.Username);
            }

            return new CommandResponse
            {
                Success = authResult.IsSuccess,
                ResponseType = authResult.IsSuccess ? (byte)0x01 : CommandDefinitions.RESTYPE_SEARCHERROR,
                Data = authResult.ResponseData ?? Array.Empty<byte>(),
                ErrorMessage = authResult.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing authentication command");
            return new CommandResponse
            {
                Success = false,
                ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                ErrorMessage = "Authentication processing failed"
            };
        }
    }
}

/// <summary>
/// 搜索命令处理器
/// </summary>
public class SearchCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_SEARCH;
    
    private readonly ILogger<SearchCommandHandler> _logger;
    private readonly IDataAccess _dataAccess;

    public SearchCommandHandler(
        ILogger<SearchCommandHandler> logger,
        IDataAccess dataAccess)
    {
        _logger = logger;
        _dataAccess = dataAccess;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing search command from client {ClientIp}:{ClientPort}", 
                request.SocketInfo.ClientIp, request.SocketInfo.ClientPort);

            // 这里应该根据具体的搜索逻辑进行实现
            // 示例：解析搜索参数并执行查询
            
            return new CommandResponse
            {
                Success = true,
                ResponseType = CommandDefinitions.RESTYPE_SEARCHEND,
                Data = Array.Empty<byte>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing search command");
            return new CommandResponse
            {
                Success = false,
                ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                ErrorMessage = "Search processing failed"
            };
        }
    }
}

/// <summary>
/// DIY搜索命令处理器
/// </summary>
public class DIYSearchCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_DIYSEARCH;
    
    private readonly ILogger<DIYSearchCommandHandler> _logger;
    private readonly IDataAccess _dataAccess;

    public DIYSearchCommandHandler(
        ILogger<DIYSearchCommandHandler> logger,
        IDataAccess dataAccess)
    {
        _logger = logger;
        _dataAccess = dataAccess;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing DIY search command from client {ClientIp}:{ClientPort}", 
                request.SocketInfo.ClientIp, request.SocketInfo.ClientPort);

            return new CommandResponse
            {
                Success = true,
                ResponseType = CommandDefinitions.RESTYPE_DIYSEARCH_SQL_SUCCESS,
                Data = Array.Empty<byte>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing DIY search command");
            return new CommandResponse
            {
                Success = false,
                ResponseType = CommandDefinitions.RESTYPE_DIYSEARCH_SQL_FAIL,
                ErrorMessage = "DIY search processing failed"
            };
        }
    }
}

// 其他命令处理器的基础实现
public class TestDataManagementCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_TESTDATAMANAGEMENT;
    private readonly ILogger<TestDataManagementCommandHandler> _logger;

    public TestDataManagementCommandHandler(ILogger<TestDataManagementCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing test data management command");
        return new CommandResponse { Success = true, Data = Array.Empty<byte>() };
    }
}

public class ConfigManagementCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_CONFIG_MNG;
    private readonly ILogger<ConfigManagementCommandHandler> _logger;

    public ConfigManagementCommandHandler(ILogger<ConfigManagementCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing config management command");
        return new CommandResponse { Success = true, Data = Array.Empty<byte>() };
    }
}

public class DatabaseManagementCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_DBMANAGEMENT;
    private readonly ILogger<DatabaseManagementCommandHandler> _logger;

    public DatabaseManagementCommandHandler(ILogger<DatabaseManagementCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing database management command");
        return new CommandResponse { Success = true, Data = Array.Empty<byte>() };
    }
}

public class StatisticCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_SEARCH_STATISTIC;
    private readonly ILogger<StatisticCommandHandler> _logger;

    public StatisticCommandHandler(ILogger<StatisticCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing statistic command");
        return new CommandResponse { Success = true, Data = Array.Empty<byte>() };
    }
}

public class UserManagementCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_USER_MNG;
    private readonly ILogger<UserManagementCommandHandler> _logger;

    public UserManagementCommandHandler(ILogger<UserManagementCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing user management command");
        return new CommandResponse { Success = true, Data = Array.Empty<byte>() };
    }
}

public class CommunityManagementCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.CMD1_COMMUNITY_MNG;
    private readonly ILogger<CommunityManagementCommandHandler> _logger;

    public CommunityManagementCommandHandler(ILogger<CommunityManagementCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing community management command");
        return new CommandResponse { Success = true, Data = Array.Empty<byte>() };
    }
}

/// <summary>
/// 认证用户名命令处理器 - 对应C++中的AuthenDealUser
/// </summary>
public class AuthenticationUserCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.REQTYPE_AUTHENTICATION_USER;

    private readonly ILogger<AuthenticationUserCommandHandler> _logger;
    private readonly IAuthenticationHandler _authenticationHandler;

    public AuthenticationUserCommandHandler(
        ILogger<AuthenticationUserCommandHandler> logger,
        IAuthenticationHandler authenticationHandler)
    {
        _logger = logger;
        _authenticationHandler = authenticationHandler;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing authentication user command from client {ClientIp}:{ClientPort}",
                request.SocketInfo.ClientIp, request.SocketInfo.ClientPort);

            // 解析用户名数据
            var authResult = await _authenticationHandler.HandleUsernameAuthenticationAsync(
                request.Data, request.DataLength, request.SocketInfo, cancellationToken);

            // 如果用户名验证成功，更新 SocketInfo 状态
            if (authResult.IsSuccess && authResult.UserInfo != null)
            {
                request.SocketInfo.UserInfo = authResult.UserInfo;

                _logger.LogDebug("Username verified for client {ClientIp}:{ClientPort}, user: {Username}",
                    request.SocketInfo.ClientIp, request.SocketInfo.ClientPort, authResult.UserInfo.Username);
            }

            return new CommandResponse
            {
                Success = authResult.IsSuccess,
                ResponseType = authResult.IsSuccess ? CommandDefinitions.RESTYPE_AUTHENTICATION_RESULT : CommandDefinitions.RESTYPE_SEARCHERROR,
                Data = authResult.ResponseData ?? Array.Empty<byte>(),
                ErrorMessage = authResult.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing authentication user command");
            return new CommandResponse
            {
                Success = false,
                ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                ErrorMessage = "Authentication user processing failed"
            };
        }
    }
}

/// <summary>
/// 认证密码命令处理器 - 对应C++中的AuthenDealPassWord
/// </summary>
public class AuthenticationPasswordCommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.REQTYPE_AUTHENTICATION_PASSWORD;

    private readonly ILogger<AuthenticationPasswordCommandHandler> _logger;
    private readonly IAuthenticationHandler _authenticationHandler;

    public AuthenticationPasswordCommandHandler(
        ILogger<AuthenticationPasswordCommandHandler> logger,
        IAuthenticationHandler authenticationHandler)
    {
        _logger = logger;
        _authenticationHandler = authenticationHandler;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing authentication password command from client {ClientIp}:{ClientPort}",
                request.SocketInfo.ClientIp, request.SocketInfo.ClientPort);

            // 解析密码数据
            var authResult = await _authenticationHandler.HandlePasswordAuthenticationAsync(
                request.Data, request.DataLength, request.SocketInfo, cancellationToken);

            // 如果密码验证成功，更新认证状态
            if (authResult.IsSuccess && authResult.UserInfo != null)
            {
                request.SocketInfo.IsAuthenticated = true;
                request.SocketInfo.UserInfo = authResult.UserInfo;

                _logger.LogInformation("Client {ClientIp}:{ClientPort} authenticated successfully as user {Username}",
                    request.SocketInfo.ClientIp, request.SocketInfo.ClientPort, authResult.UserInfo.Username);
            }

            return new CommandResponse
            {
                Success = authResult.IsSuccess,
                ResponseType = authResult.IsSuccess ? CommandDefinitions.RESTYPE_AUTHENTICATION_RESULT : CommandDefinitions.RESTYPE_SEARCHERROR,
                Data = authResult.ResponseData ?? Array.Empty<byte>(),
                ErrorMessage = authResult.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing authentication password command");
            return new CommandResponse
            {
                Success = false,
                ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
                ErrorMessage = "Authentication password processing failed"
            };
        }
    }
}

/// <summary>
/// 认证用户名2命令处理器 - 对应C++中的AuthenDealUser2
/// </summary>
public class AuthenticationUser2CommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.REQTYPE_AUTHENTICATION_USER2;

    private readonly ILogger<AuthenticationUser2CommandHandler> _logger;

    public AuthenticationUser2CommandHandler(ILogger<AuthenticationUser2CommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing authentication user2 command - not implemented");
        return new CommandResponse
        {
            Success = false,
            ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
            ErrorMessage = "User2 authentication not implemented"
        };
    }
}

/// <summary>
/// 认证密码2命令处理器 - 对应C++中的AuthenDealPassWord2
/// </summary>
public class AuthenticationPassword2CommandHandler : ICommandHandler
{
    public byte CommandId => CommandDefinitions.REQTYPE_AUTHENTICATION_PASSWORD2;

    private readonly ILogger<AuthenticationPassword2CommandHandler> _logger;

    public AuthenticationPassword2CommandHandler(ILogger<AuthenticationPassword2CommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing authentication password2 command - not implemented");
        return new CommandResponse
        {
            Success = false,
            ResponseType = CommandDefinitions.RESTYPE_SEARCHERROR,
            ErrorMessage = "Password2 authentication not implemented"
        };
    }
}
