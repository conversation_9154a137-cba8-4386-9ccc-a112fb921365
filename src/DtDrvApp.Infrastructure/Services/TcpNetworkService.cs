using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using DtDrvApp.Core.Configuration;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// TCP网络服务实现 - 对应C++中的PPM_Tcp_Server
/// </summary>
public class TcpNetworkService : INetworkService
{
    private readonly ILogger<TcpNetworkService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IOptions<CommunicationOptions> _options;
    
    private TcpListener? _tcpListener;
    private TcpListener? _ipv6TcpListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private readonly ConcurrentDictionary<string, ClientConnection> _activeConnections = new();
    private int _maxClientCount = 0;
    private readonly object _lockObject = new();

    public TcpNetworkService(
        ILogger<TcpNetworkService> logger,
        IServiceProvider serviceProvider,
        IOptions<CommunicationOptions> options)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _options = options;
    }

    public bool IsIPv6Enabled => _options.Value.IsIPv6Enable;
    public int Port => _options.Value.DataPort;

    /// <summary>
    /// 启动服务
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            
            if (IsIPv6Enabled)
            {
                await StartIPv6ServerAsync(_cancellationTokenSource.Token);
            }
            else
            {
                await StartIPv4ServerAsync(_cancellationTokenSource.Token);
            }

            _logger.LogInformation("TCP network service started on port {Port}, IPv6: {IPv6Enabled}", 
                Port, IsIPv6Enabled);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start TCP network service");
            throw;
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Stopping TCP network service...");

            // 取消所有操作
            _cancellationTokenSource?.Cancel();

            // 停止监听器
            _tcpListener?.Stop();
            _ipv6TcpListener?.Stop();

            // 关闭所有活动连接
            var closeTasks = _activeConnections.Values.Select(conn => CloseConnectionAsync(conn));
            await Task.WhenAll(closeTasks);

            _activeConnections.Clear();

            _logger.LogInformation("TCP network service stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping TCP network service");
        }
        finally
        {
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
    }

    /// <summary>
    /// 获取客户端连接数
    /// </summary>
    public int GetClientCount()
    {
        return _activeConnections.Count;
    }

    /// <summary>
    /// 获取客户端连接数峰值
    /// </summary>
    public int GetMaxClientCount()
    {
        lock (_lockObject)
        {
            return _maxClientCount;
        }
    }

    /// <summary>
    /// 重置客户端连接数峰值
    /// </summary>
    public void ResetClientCount()
    {
        lock (_lockObject)
        {
            _maxClientCount = _activeConnections.Count;
        }
    }

    /// <summary>
    /// 启动IPv4服务器
    /// </summary>
    private async Task StartIPv4ServerAsync(CancellationToken cancellationToken)
    {
        var endpoint = new IPEndPoint(IPAddress.Any, Port);
        _tcpListener = new TcpListener(endpoint);
        _tcpListener.Start();

        _logger.LogInformation("IPv4 TCP server listening on {Endpoint}", endpoint);

        _ = Task.Run(async () => await AcceptClientsAsync(_tcpListener, cancellationToken), cancellationToken);
    }

    /// <summary>
    /// 启动IPv6服务器
    /// </summary>
    private async Task StartIPv6ServerAsync(CancellationToken cancellationToken)
    {
        var endpoint = new IPEndPoint(IPAddress.IPv6Any, Port);
        _ipv6TcpListener = new TcpListener(endpoint);
        _ipv6TcpListener.Start();

        _logger.LogInformation("IPv6 TCP server listening on {Endpoint}", endpoint);

        _ = Task.Run(async () => await AcceptClientsAsync(_ipv6TcpListener, cancellationToken), cancellationToken);
    }

    /// <summary>
    /// 接受客户端连接
    /// </summary>
    private async Task AcceptClientsAsync(TcpListener listener, CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var tcpClient = await listener.AcceptTcpClientAsync();
                
                var clientEndpoint = tcpClient.Client.RemoteEndPoint as IPEndPoint;
                var clientKey = $"{clientEndpoint?.Address}:{clientEndpoint?.Port}";

                _logger.LogInformation("New client connected: {ClientKey}", clientKey);

                var connection = new ClientConnection
                {
                    TcpClient = tcpClient,
                    ClientKey = clientKey,
                    ConnectTime = DateTime.UtcNow,
                    SocketInfo = new SocketInfo
                    {
                        ClientIp = clientEndpoint?.Address?.ToString() ?? "unknown",
                        ClientPort = clientEndpoint?.Port ?? 0,
                        ConnectTime = DateTime.UtcNow,
                        IsAuthenticated = false
                    }
                };

                _activeConnections[clientKey] = connection;
                UpdateMaxClientCount();

                // 为每个客户端启动处理任务
                _ = Task.Run(async () => await HandleClientAsync(connection, cancellationToken), cancellationToken);
            }
        }
        catch (ObjectDisposedException)
        {
            // 监听器已被释放，正常退出
        }
        catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogError(ex, "Error accepting client connections");
        }
    }

    /// <summary>
    /// 处理客户端连接
    /// </summary>
    private async Task HandleClientAsync(ClientConnection connection, CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var socketHandler = scope.ServiceProvider.GetRequiredService<ISocketHandler>();

            // 开始处理连接
            await socketHandler.BeginHandleAsync(connection.SocketInfo, cancellationToken);

            var buffer = new byte[4096];
            var stream = connection.TcpClient.GetStream();

            while (!cancellationToken.IsCancellationRequested && connection.TcpClient.Connected)
            {
                try
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    
                    if (bytesRead == 0)
                    {
                        // 客户端断开连接
                        break;
                    }

                    // 处理接收到的数据
                    var success = await socketHandler.HandleDataAsync(buffer, bytesRead, connection.SocketInfo, cancellationToken);
                    
                    if (!success)
                    {
                        _logger.LogWarning("Failed to handle data from client: {ClientKey}", connection.ClientKey);
                        break;
                    }
                }
                catch (IOException ex)
                {
                    _logger.LogDebug(ex, "Client {ClientKey} disconnected", connection.ClientKey);
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error handling client {ClientKey}", connection.ClientKey);
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in client handler for {ClientKey}", connection.ClientKey);
        }
        finally
        {
            await CloseConnectionAsync(connection);
        }
    }

    /// <summary>
    /// 关闭连接
    /// </summary>
    private async Task CloseConnectionAsync(ClientConnection connection)
    {
        try
        {
            _activeConnections.TryRemove(connection.ClientKey, out _);
            
            if (connection.TcpClient.Connected)
            {
                connection.TcpClient.Close();
            }

            connection.TcpClient.Dispose();

            _logger.LogInformation("Client {ClientKey} disconnected", connection.ClientKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing connection for client {ClientKey}", connection.ClientKey);
        }
    }

    /// <summary>
    /// 更新最大客户端连接数
    /// </summary>
    private void UpdateMaxClientCount()
    {
        lock (_lockObject)
        {
            var currentCount = _activeConnections.Count;
            if (currentCount > _maxClientCount)
            {
                _maxClientCount = currentCount;
            }
        }
    }
}

/// <summary>
/// 客户端连接信息
/// </summary>
internal class ClientConnection
{
    public required TcpClient TcpClient { get; set; }
    public required string ClientKey { get; set; }
    public DateTime ConnectTime { get; set; }
    public required SocketInfo SocketInfo { get; set; }
}
