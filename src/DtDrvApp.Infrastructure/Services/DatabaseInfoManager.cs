using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using DtDrvApp.Infrastructure.Data;
using Microsoft.Extensions.Logging;

namespace DtDrvApp.Infrastructure.Services;

/// <summary>
/// 数据库信息管理器实现 - 对应C++中的CDataBaseInfo
/// </summary>
public class DatabaseInfoManager : IDatabaseInfoManager
{
    private readonly DtDbContext _context;
    private readonly ILogger<DatabaseInfoManager> _logger;
    private readonly ICryptoService _cryptoService;
    
    private DatabaseConnectionInfo? _mainDatabaseConnection;
    private readonly Dictionary<int, DatabaseConnectionInfo> _databaseConnections = new();
    private readonly Dictionary<int, TestType> _projectSettings = new();
    private readonly Dictionary<int, UserStatus> _userStatuses = new();
    
    private bool _isAccountControlEnabled = false;
    private int _passwordValidDays = 90;
    private int _maxTryTimes = 3;
    private int _alarmDays = 7;

    public DatabaseInfoManager(
        DtDbContext context,
        ILogger<DatabaseInfoManager> logger,
        ICryptoService cryptoService)
    {
        _context = context;
        _logger = logger;
        _cryptoService = cryptoService;
    }

    /// <summary>
    /// 获取用户状态
    /// </summary>
    public async Task<UserStatus?> GetUserStatusAsync(int userId)
    {
        try
        {
            if (_userStatuses.TryGetValue(userId, out var status))
            {
                return status;
            }

            var user = await _context.Users
                .Where(u => u.Id == userId && u.IsActive)
                .FirstAsync();

            if (user == null)
            {
                return null;
            }

            var userStatus = new UserStatus
            {
                UserId = user.Id,
                Username = user.Username,
                IsAuthenticated = false,
                AuthenticationStep = 0,
                LoginTime = DateTime.UtcNow,
                ClientIp = string.Empty,
                DatabaseId = user.DatabaseId ?? 0
            };

            _userStatuses[userId] = userStatus;
            return userStatus;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user status for user ID: {UserId}", userId);
            return null;
        }
    }

    /// <summary>
    /// 检查数据库连接信息是否存在
    /// </summary>
    public async Task<bool> IsDbConnectionInfoExistAsync(int databaseId)
    {
        try
        {
            if (_databaseConnections.ContainsKey(databaseId))
            {
                return true;
            }

            var exists = await _context.DatabaseConnections
                .Where(dc => dc.Id == databaseId && dc.IsActive)
                .AnyAsync();

            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking database connection existence for ID: {DatabaseId}", databaseId);
            return false;
        }
    }

    /// <summary>
    /// 根据项目ID获取测试类型
    /// </summary>
    public async Task<TestType> GetTestTypeByProjectIdAsync(int projectId)
    {
        try
        {
            if (_projectSettings.TryGetValue(projectId, out var testType))
            {
                return testType;
            }

            var project = await _context.ProjectSettings
                .Where(ps => ps.ProjectId == projectId && ps.IsActive)
                .FirstAsync();

            if (project != null)
            {
                var type = (TestType)project.TestType;
                _projectSettings[projectId] = type;
                return type;
            }

            return TestType.TEST_DOC; // 默认值
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test type for project ID: {ProjectId}", projectId);
            return TestType.TEST_DOC;
        }
    }

    /// <summary>
    /// 获取密码失效天数
    /// </summary>
    public async Task<int> GetPasswordInvalidDaysAsync()
    {
        try
        {
            var config = await _context.SystemConfigurations
                .Where(sc => sc.ConfigKey == "PasswordValidDays" && sc.IsActive)
                .FirstAsync();

            if (config != null && int.TryParse(config.ConfigValue, out var days))
            {
                _passwordValidDays = days;
            }

            return _passwordValidDays;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting password invalid days");
            return _passwordValidDays;
        }
    }

    /// <summary>
    /// 读取数据
    /// </summary>
    public async Task<bool> ReadDataAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting to read database information...");

            // 读取数据库定义
            if (!await ReadDatabaseDefinitionsAsync(cancellationToken))
            {
                _logger.LogError("Failed to read database definitions");
                return false;
            }

            // 读取项目定义
            if (!await ReadProjectDefinitionsAsync(cancellationToken))
            {
                _logger.LogError("Failed to read project definitions");
                return false;
            }

            // 读取账户控制设置
            await ReadAccountControlSettingsAsync(cancellationToken);

            _logger.LogInformation("Database information loaded successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading database information");
            return false;
        }
    }

    /// <summary>
    /// 设置数据库连接信息
    /// </summary>
    public void SetDatabaseConnectionInfo(DatabaseConnectionInfo connectionInfo)
    {
        _mainDatabaseConnection = connectionInfo ?? throw new ArgumentNullException(nameof(connectionInfo));
        _logger.LogDebug("Main database connection info set for server: {Server}", connectionInfo.ServerAddress);
    }

    /// <summary>
    /// 获取数据库连接信息
    /// </summary>
    public async Task<DatabaseConnectionInfo?> GetDatabaseConnectionInfoAsync(int databaseId)
    {
        try
        {
            if (_databaseConnections.TryGetValue(databaseId, out var connectionInfo))
            {
                return connectionInfo;
            }

            var dbConnection = await _context.DatabaseConnections
                .Where(dc => dc.Id == databaseId && dc.IsActive)
                .FirstAsync();

            if (dbConnection == null)
            {
                return null;
            }

            // 解密密码
            var decryptedPassword = _cryptoService.DesDecrypt(dbConnection.Password, "default_key");

            var connection = new DatabaseConnectionInfo
            {
                ServerAddress = dbConnection.ServerAddress,
                DatabaseName = dbConnection.DatabaseName,
                Username = dbConnection.Username,
                Password = decryptedPassword,
                Port = dbConnection.Port,
                DatabaseType = (DatabaseType)dbConnection.DatabaseType,
                DisplaySql = dbConnection.DisplaySql
            };

            _databaseConnections[databaseId] = connection;
            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting database connection info for ID: {DatabaseId}", databaseId);
            return null;
        }
    }

    /// <summary>
    /// 读取数据库定义
    /// </summary>
    private async Task<bool> ReadDatabaseDefinitionsAsync(CancellationToken cancellationToken)
    {
        try
        {
            var connections = await _context.DatabaseConnections
                .Where(dc => dc.IsActive)
                .ToListAsync();

            _databaseConnections.Clear();
            foreach (var connection in connections)
            {
                try
                {
                    // 解密密码
                    var decryptedPassword = _cryptoService.DesDecrypt(connection.Password, "default_key");

                    var connectionInfo = new DatabaseConnectionInfo
                    {
                        ServerAddress = connection.ServerAddress,
                        DatabaseName = connection.DatabaseName,
                        Username = connection.Username,
                        Password = decryptedPassword,
                        Port = connection.Port,
                        DatabaseType = (DatabaseType)connection.DatabaseType,
                        DisplaySql = connection.DisplaySql
                    };

                    _databaseConnections[connection.Id] = connectionInfo;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to decrypt password for database connection ID: {Id}", connection.Id);
                }
            }

            _logger.LogInformation("Loaded {Count} database connections", _databaseConnections.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading database definitions");
            return false;
        }
    }

    /// <summary>
    /// 读取项目定义
    /// </summary>
    private async Task<bool> ReadProjectDefinitionsAsync(CancellationToken cancellationToken)
    {
        try
        {
            var projects = await _context.ProjectSettings
                .Where(ps => ps.IsActive)
                .ToListAsync();

            _projectSettings.Clear();
            foreach (var project in projects)
            {
                _projectSettings[project.ProjectId] = (TestType)project.TestType;
            }

            _logger.LogInformation("Loaded {Count} project settings", _projectSettings.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading project definitions");
            return false;
        }
    }

    /// <summary>
    /// 读取账户控制设置
    /// </summary>
    private async Task ReadAccountControlSettingsAsync(CancellationToken cancellationToken)
    {
        try
        {
            var configs = await _context.SystemConfigurations
                .Where(sc => sc.IsActive && (
                    sc.ConfigKey == "EnableAccountControl" ||
                    sc.ConfigKey == "PasswordValidDays" ||
                    sc.ConfigKey == "MaxTryTimes" ||
                    sc.ConfigKey == "AlarmDays"))
                .ToListAsync();

            foreach (var config in configs)
            {
                switch (config.ConfigKey)
                {
                    case "EnableAccountControl":
                        _isAccountControlEnabled = bool.TryParse(config.ConfigValue, out var enabled) && enabled;
                        break;
                    case "PasswordValidDays":
                        if (int.TryParse(config.ConfigValue, out var validDays))
                            _passwordValidDays = validDays;
                        break;
                    case "MaxTryTimes":
                        if (int.TryParse(config.ConfigValue, out var maxTries))
                            _maxTryTimes = maxTries;
                        break;
                    case "AlarmDays":
                        if (int.TryParse(config.ConfigValue, out var alarmDays))
                            _alarmDays = alarmDays;
                        break;
                }
            }

            _logger.LogDebug("Account control settings loaded - Enabled: {Enabled}, ValidDays: {ValidDays}, MaxTries: {MaxTries}, AlarmDays: {AlarmDays}",
                _isAccountControlEnabled, _passwordValidDays, _maxTryTimes, _alarmDays);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading account control settings");
        }
    }
}
