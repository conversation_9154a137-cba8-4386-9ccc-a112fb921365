using DtDrvApp.Infrastructure.Data.Entities;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace DtDrvApp.Infrastructure.Data;

/// <summary>
/// SqlSugar 数据库上下文
/// </summary>
public class DtDbContext : IDisposable
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<DtDbContext> _logger;
    private bool _disposed = false;

    public DtDbContext(ISqlSugarClient db, ILogger<DtDbContext> logger)
    {
        _db = db;
        _logger = logger;
    }

    // 用户相关表
    public ISugarQueryable<User> Users => _db.Queryable<User>();
    public ISugarQueryable<UserRole> UserRoles => _db.Queryable<UserRole>();
    public ISugarQueryable<Role> Roles => _db.Queryable<Role>();
    public ISugarQueryable<RoleFunction> RoleFunctions => _db.Queryable<RoleFunction>();
    public ISugarQueryable<Function> Functions => _db.Queryable<Function>();
    public ISugarQueryable<SubFunction> SubFunctions => _db.Queryable<SubFunction>();

    // 配置相关表
    public ISugarQueryable<DatabaseConnection> DatabaseConnections => _db.Queryable<DatabaseConnection>();
    public ISugarQueryable<SystemConfiguration> SystemConfigurations => _db.Queryable<SystemConfiguration>();
    public ISugarQueryable<ProjectSetting> ProjectSettings => _db.Queryable<ProjectSetting>();

    // 日志相关表
    public ISugarQueryable<SystemOperationLog> SystemOperationLogs => _db.Queryable<SystemOperationLog>();
    public ISugarQueryable<UserLoginLog> UserLoginLogs => _db.Queryable<UserLoginLog>();

    // 数据表配置
    public ISugarQueryable<TableConfiguration> TableConfigurations => _db.Queryable<TableConfiguration>();
    public ISugarQueryable<ColumnConfiguration> ColumnConfigurations => _db.Queryable<ColumnConfiguration>();

    /// <summary>
    /// 获取 SqlSugar 客户端
    /// </summary>
    public ISqlSugarClient Database => _db;

    /// <summary>
    /// 初始化数据库表结构
    /// </summary>
    public async Task InitializeDatabaseAsync()
    {
        try
        {
            _logger.LogInformation("Starting database initialization...");

            // 先测试数据库连接
            _logger.LogInformation("Testing database connection...");
            var testResult = await Task.Run(() => _db.Ado.GetString("SELECT 1"));
            _logger.LogInformation("Database connection successful");

            // 创建表（如果不存在）
            _logger.LogInformation("Creating database tables...");
            _db.CodeFirst.InitTables(
                typeof(User),
                typeof(Role),
                typeof(UserRole),
                typeof(Function),
                typeof(SubFunction),
                typeof(RoleFunction),
                typeof(DatabaseConnection),
                typeof(SystemConfiguration),
                typeof(ProjectSetting),
                typeof(SystemOperationLog),
                typeof(UserLoginLog),
                typeof(TableConfiguration),
                typeof(ColumnConfiguration)
            );

            _logger.LogInformation("Database tables created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database: {Message}", ex.Message);
            throw new InvalidOperationException("Database initialization failed. Please check database connection and configuration.", ex);
        }
    }

    /// <summary>
    /// 测试数据库连接
    /// </summary>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing database connection...");
            var result = await Task.Run(() => _db.Ado.GetString("SELECT 1"));
            _logger.LogInformation("Database connection test successful");
            return !string.IsNullOrEmpty(result);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Database connection test failed: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 开始事务
    /// </summary>
    public void BeginTransaction()
    {
        _db.Ado.BeginTran();
    }

    /// <summary>
    /// 提交事务
    /// </summary>
    public void CommitTransaction()
    {
        _db.Ado.CommitTran();
    }

    /// <summary>
    /// 回滚事务
    /// </summary>
    public void RollbackTransaction()
    {
        _db.Ado.RollbackTran();
    }

    /// <summary>
    /// 执行原生SQL
    /// </summary>
    public async Task<int> ExecuteSqlAsync(string sql, object? parameters = null)
    {
        if (parameters != null)
        {
            return await _db.Ado.ExecuteCommandAsync(sql, parameters);
        }
        return await _db.Ado.ExecuteCommandAsync(sql);
    }

    /// <summary>
    /// 查询原生SQL
    /// </summary>
    public async Task<List<T>> QuerySqlAsync<T>(string sql, object? parameters = null)
    {
        if (parameters != null)
        {
            return await _db.Ado.SqlQueryAsync<T>(sql, parameters);
        }
        return await _db.Ado.SqlQueryAsync<T>(sql);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _db?.Dispose();
            _disposed = true;
        }
    }
}
