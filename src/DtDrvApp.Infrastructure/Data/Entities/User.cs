using SqlSugar;

namespace DtDrvApp.Infrastructure.Data.Entities;

/// <summary>
/// 用户实体
/// </summary>
[SugarTable("Users")]
public class User
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(Length = 50, IsNullable = false)]
    public string Username { get; set; } = string.Empty;

    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "登录代码")]
    public string LoginCode { get; set; } = string.Empty;

    [SugarColumn(Length = 100, IsNullable = false)]
    public string Password { get; set; } = string.Empty;

    [SugarColumn(Length = 20, IsNullable = true)]
    public string? Phone { get; set; }

    [SugarColumn(Length = 500, IsNullable = true)]
    public string? Comment { get; set; }

    [SugarColumn(IsNullable = false, ColumnDescription = "是否激活")]
    public bool IsActive { get; set; } = true;

    [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
    public DateTime CreatedAt { get; set; }

    [SugarColumn(IsNullable = true, ColumnDescription = "更新时间")]
    public DateTime? UpdatedAt { get; set; }

    [SugarColumn(IsNullable = true, ColumnDescription = "最后登录时间")]
    public DateTime? LastLoginTime { get; set; }

    [SugarColumn(Length = 45, IsNullable = true, ColumnDescription = "最后登录IP")]
    public string? LastLoginIp { get; set; }

    [SugarColumn(IsNullable = true, ColumnDescription = "数据库ID")]
    public int? DatabaseId { get; set; }

    // 导航属性 - SqlSugar 不需要 virtual 关键字
    [SugarColumn(IsIgnore = true)]
    public List<UserRole> UserRoles { get; set; } = new List<UserRole>();
}

/// <summary>
/// 角色实体
/// </summary>
[SugarTable("Roles")]
public class Role
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(Length = 50, IsNullable = false)]
    public string RoleName { get; set; } = string.Empty;

    [SugarColumn(Length = 500, IsNullable = true)]
    public string? Description { get; set; }

    [SugarColumn(IsNullable = false)]
    public bool IsActive { get; set; } = true;

    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; }

    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedAt { get; set; }

    // 导航属性
    [SugarColumn(IsIgnore = true)]
    public List<UserRole> UserRoles { get; set; } = new List<UserRole>();

    [SugarColumn(IsIgnore = true)]
    public List<RoleFunction> RoleFunctions { get; set; } = new List<RoleFunction>();
}

/// <summary>
/// 用户角色关联实体
/// </summary>
[SugarTable("UserRoles")]
public class UserRole
{
    [SugarColumn(IsPrimaryKey = true, IsNullable = false)]
    public int UserId { get; set; }

    [SugarColumn(IsPrimaryKey = true, IsNullable = false)]
    public int RoleId { get; set; }

    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; }

    // 导航属性
    [SugarColumn(IsIgnore = true)]
    public User User { get; set; } = null!;

    [SugarColumn(IsIgnore = true)]
    public Role Role { get; set; } = null!;
}

/// <summary>
/// 功能实体
/// </summary>
public class Function
{
    public int Id { get; set; }
    public string FunctionName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // 导航属性
    public virtual ICollection<SubFunction> SubFunctions { get; set; } = new List<SubFunction>();
}

/// <summary>
/// 子功能实体
/// </summary>
public class SubFunction
{
    public int Id { get; set; }
    public int FunctionId { get; set; }
    public string SubFunctionName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // 导航属性
    public virtual Function Function { get; set; } = null!;
}

/// <summary>
/// 角色功能关联实体
/// </summary>
public class RoleFunction
{
    public int RoleId { get; set; }
    public int FunctionId { get; set; }
    public int SubFunctionId { get; set; }
    public DateTime CreatedAt { get; set; }

    // 导航属性
    public virtual Role Role { get; set; } = null!;
    public virtual Function Function { get; set; } = null!;
    public virtual SubFunction SubFunction { get; set; } = null!;
}

/// <summary>
/// 数据库连接实体
/// </summary>
public class DatabaseConnection
{
    public int Id { get; set; }
    public string ServerAddress { get; set; } = string.Empty;
    public string DatabaseName { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty; // 加密存储
    public int Port { get; set; } = 1433;
    public int DatabaseType { get; set; } = 0; // 0: SQL Server, 1: MySQL
    public bool DisplaySql { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 系统配置实体
/// </summary>
public class SystemConfiguration
{
    public int Id { get; set; }
    public string ConfigKey { get; set; } = string.Empty;
    public string? ConfigValue { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 项目设置实体
/// </summary>
public class ProjectSetting
{
    public int Id { get; set; }
    public int ProjectId { get; set; }
    public string? ProjectName { get; set; }
    public int TestType { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 系统操作日志实体
/// </summary>
public class SystemOperationLog
{
    public long Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string UserIp { get; set; } = string.Empty;
    public int EventTypeId { get; set; }
    public int FunctionId { get; set; }
    public int SubFunctionId { get; set; }
    public string? EventDescription { get; set; }
    public int CityId { get; set; }
    public DateTime OperationTime { get; set; }
}

/// <summary>
/// 用户登录日志实体
/// </summary>
public class UserLoginLog
{
    public long Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string LoginIp { get; set; } = string.Empty;
    public DateTime LoginTime { get; set; }
    public bool IsSuccess { get; set; }
    public string? EventDescription { get; set; }
}

/// <summary>
/// 表配置实体
/// </summary>
public class TableConfiguration
{
    public int Id { get; set; }
    public int TableId { get; set; }
    public string TableName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // 导航属性
    public virtual ICollection<ColumnConfiguration> ColumnConfigurations { get; set; } = new List<ColumnConfiguration>();
}

/// <summary>
/// 列配置实体
/// </summary>
public class ColumnConfiguration
{
    public int Id { get; set; }
    public int TableId { get; set; }
    public string ColumnName { get; set; } = string.Empty;
    public string? ColumnType { get; set; }
    public int ColumnOrder { get; set; }
    public int MaxLength { get; set; }
    public bool IsNullable { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // 导航属性
    public virtual TableConfiguration TableConfiguration { get; set; } = null!;
}
