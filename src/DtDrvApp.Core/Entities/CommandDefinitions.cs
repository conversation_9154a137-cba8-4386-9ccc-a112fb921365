namespace DtDrvApp.Core.Entities;

/// <summary>
/// 命令字定义 - 对应C++中的CommandDefine.h
/// </summary>
public static class CommandDefinitions
{
    // 命令字1 - 主命令
    public const byte CMD1_AUTHENTICATION = 0x01;      // 用户鉴权命令字
    public const byte CMD1_TESTDATAMANAGEMENT = 0x02;  // 测试数据维护命令字
    public const byte CMD1_CONFIG_MNG = 0x03;          // 配置管理命令字
    public const byte CMD1_DBMANAGEMENT = 0x04;        // 数据库管理命令字
    public const byte CMD1_SEARCH = 0x06;              // 信息查询命令字
    public const byte CMD1_SEARCH_STATISTIC = 0x07;    // 统计信息查询命令字
    public const byte CMD1_USER_MNG = 0x0a;            // 用户权限管理命令字
    public const byte CMD1_COMMUNITY_MNG = 0x0b;       // 楼宇信号分析命令字
    public const byte CMD1_DIYSEARCH = 0x0c;           // 自定义查询操作

    // 命令字2 - 子命令
    public const byte CMD2_REQUEST = 0x00;             // 请求
    public const byte CMD2_RESPONSE = 0x01;            // 响应

    // 响应类型
    public const byte RESTYPE_SEARCHEND = 0xE0;        // 查询信息结束
    public const byte RESTYPE_SEARCHERROR = 0xEE;      // 查询中错误

    // DIY搜索响应类型
    public const byte RESTYPE_DIYSEARCH_SQL = 0x20;
    public const byte RESTYPE_DIYSEARCH_SQL_SUCCESS = 0x21;
    public const byte RESTYPE_DIYSEARCH_SQL_FAIL = 0x22;

    // 用户管理响应类型
    public const byte RESTYPE_USER_MNG_FUNC = 0x30;
    public const byte RESTYPE_USER_MNG_SUBFUNC = 0x31;
    public const byte RESTYPE_USER_MNG_ROLE = 0x32;
    public const byte RESTYPE_USER_MNG_ROLE_FUNC = 0x33;
    public const byte RESTYPE_USER_MNG_USER_ROLE = 0x34;

    // 表数据响应类型
    public const byte RESTYPE_TABLE_DATA_INFO = 0x40;
    public const byte RESTYPE_COLUMN_MODEL_TABLE = 0x41;
    public const byte RESTYPE_DIY_MODEL_TABLE = 0x42;
}

/// <summary>
/// 测试类型枚举 - 对应C++中的eTESTTYPE
/// </summary>
public enum TestType
{
    TEST_DOC = 0,
    TEST_DT = 1,
    TEST_CQT = 2,
    TEST_AUTODT = 3,
    TEST_TA = 4,
    TEST_SC = 5,
    TEST_AUTOCQT = 6,
    TEST_CDMADT = 7,
    TEST_CDMACQT = 8,
    TEST_TDSCDMADT = 9,
    TEST_TDSCDMACQT = 10,
    TEST_WCDMADT = 11,
    TEST_WCDMACQT = 12,
    TEST_CDMA2000DT = 13,
    TEST_CDMA2000CQT = 14,
    TEST_CALLTRACE = 15,
    TEST_WLANDT = 16,
    TEST_WLANCQT = 17,
    TEST_ATUCQT = 18,
    TEST_LTE_TDD_DT = 19,
    TEST_LTE_TDD_CQT = 20,
    TEST_LTE_UEP = 21,
    TEST_LTE_FDD_DT = 22,
    TEST_LTE_FDD_CQT = 23,
    TEST_LTE_SIGNAL_DT = 24,
    TEST_LTE_SIGNAL_CQT = 25,
    TEST_NBIOT_DT = 26,
    TEST_NBIOT_CQT = 27,
    TEST_NR_TDD_DT = 28,
    TEST_NR_TDD_CQT = 29
}

/// <summary>
/// 文件类型枚举 - 对应C++中的文件类型定义
/// </summary>
public enum FileType
{
    FILE_UNKNOWN = 0,
    FILE_HUAXING_FGS = 1,      // 华星*.fgs
    FILE_STD_LOT = 2,          // 标准格式*.lot
    FILE_HXMOS_CSV = 3,
    FILE_HUAXING_WR = 4,       // 华星Wlan*.wr
    FILE_CDS_CDF = 5,
    FILE_TEMS_CTR = 6,
    FILE_NEMO_NBL = 7,
    FILE_STD_LOW = 8,
    FILE_TEMS_QMD = 9,
    FILE_TEMS_QMZ = 10,
    FILE_STD_LOE = 11,
    FILE_DINGLI_DCF = 12,      // 鼎力DCF数据
    FILE_HUAWEI_GEN2CSV = 13,  // 华为Probe导出的csv文件
    FILE_RS_ASC_NB = 14,       // 罗德斯瓦次asc NB扫频
    FILE_MINGRUN_MRM = 15,     // 铭润mrm文件
    FILE_KNOWYOU_CU = 16,      // 诺优*.cu
    FILE_HUGELAND_CU = 17,     // 惠捷朗CDS软件生成的*.cu文件
    FILE_FENGHUO_CSV = 18,     // 黑龙江烽火设备导出的CSV文件
    FILE_PCTEL2CSV = 19,       // PCTEL设备转换出的CSV文件
    FILE_ZTE_APS2CSV = 20,     // 中兴aps文件转换出的CSV文件
    FILE_DINGLI_CU = 21,       // 鼎利软件测试出来的cu文件，可回放
    FILE_SPARK_RAF = 22,       // 新科思创Spark软件测试的.raf文件
    FILE_STD_L5G = 23,         // L5G文件
    FILE_KNOWYOU_NRSCAN2CSV = 24, // 诺优 NR 扫频数据类型
    FILE_SPIDER_HL5G = 25,     // 上海 小蜘蛛 .hl5g文件
    FILE_SPIDER_HLNR = 26,     // 上海 小蜘蛛 .hlnr文件
    FILE_SPIDER_HLTE = 27,     // 上海 小蜘蛛 .hlte文件
    FILE_SPIDER_HLOG = 28,     // 上海 小蜘蛛 .hlog文件
    FILE_KNOWYOU_LTLG = 29,    // 诺优*.ltlg
    FILE_RS_SPECTRUM_CSV = 30, // 上海罗德斯瓦茨生成的用于地段计费的CSV文件
    FILE_ZTE_APM = 31,         // 新疆中兴设备 T-Phone Analyzer 测试的APM文件
    FILE_DATANG_DTM = 32,      // 大唐 ETG 软件测试生成的 .dtm文件
    FILE_NOKIA_IHANDLE_HLD = 33, // Nokia 单验工具 ihandle 生成的 .hlg文件
    FILE_SPARK_CU = 34,        // 万思维的 spark 路测软件 生成的 .cu文件
    FILE_SPARK_DTLOG = 35,     // 万思维的 spark 路测软件 转换生成的 .dtlog文件
    FILE_CHUANGYUAN_4G_SCAN_CSV = 36, // 创远扫频仪生成的 .csv 文件
    FILE_CHUANGYUAN_5G_SCAN_CSV = 37, // 创远扫频仪生成的 .csv 文件
    FILE_HUAWEI_CU = 38,       // 华为软件测试出来的cu文件，.cu文件
    FILE_APP_JSON2CSV = 39,    // 手机APP软件测试生成的json文件，转换出的CSV文件
    FILE_GENERIC_CSV = 40      // 通用的简单采样点CSV文件
}
