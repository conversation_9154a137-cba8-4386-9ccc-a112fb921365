namespace DtDrvApp.Core.Entities;

/// <summary>
/// 数据库连接信息 - 对应C++中的tDBConnInfo
/// </summary>
public class DatabaseConnectionInfo
{
    public string ServerAddress { get; set; } = string.Empty;
    public string DatabaseName { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public int Port { get; set; } = 1433;
    public DatabaseType DatabaseType { get; set; } = DatabaseType.SqlServer;
    public bool DisplaySql { get; set; } = false;
    public int CommandTimeout { get; set; } = 30;
    public int ConnectionTimeout { get; set; } = 30;
}

/// <summary>
/// 数据库类型枚举
/// </summary>
public enum DatabaseType
{
    SqlServer = 0,
    MySQL = 1,
    OceanBase = 2
}

/// <summary>
/// 数据库列信息 - 对应C++中的STRU_DBCOL
/// </summary>
public class DatabaseColumn
{
    public string ColumnName { get; set; } = string.Empty;
    public int ColumnType { get; set; }
    public int ColumnId { get; set; }
    public int MaxLength { get; set; }
    public bool IsNullable { get; set; }
}

/// <summary>
/// 用户信息 - 对应C++中的STRU_USERINFO
/// </summary>
public class UserInfo
{
    public int DatabaseId { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public DateTime? LastLoginTime { get; set; }
    public string? LastLoginIp { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 用户状态信息
/// </summary>
public class UserStatus
{
    public int UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public bool IsAuthenticated { get; set; }
    public byte AuthenticationStep { get; set; } // 0:未鉴权,1:已发送用户名,2:已发送密码
    public DateTime LoginTime { get; set; }
    public string ClientIp { get; set; } = string.Empty;
    public int DatabaseId { get; set; }
}

/// <summary>
/// Socket连接信息 - 对应C++中的STRU_SockInfo
/// </summary>
public class SocketInfo
{
    public string ClientIp { get; set; } = string.Empty;
    public int ClientPort { get; set; }
    public DateTime ConnectTime { get; set; }
    public bool IsAuthenticated { get; set; }
    public UserInfo? UserInfo { get; set; }
    public DatabaseConnectionInfo? DatabaseConnection { get; set; }
}

/// <summary>
/// 查询条件基类
/// </summary>
public abstract class SearchCondition
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public int TopLeftLongitude { get; set; }
    public int TopLeftLatitude { get; set; }
    public int BottomRightLongitude { get; set; }
    public int BottomRightLatitude { get; set; }
}

/// <summary>
/// 时间范围条件
/// </summary>
public class TimeSpan
{
    public int StartTime { get; set; }
    public int EndTime { get; set; }
}

/// <summary>
/// 搜索区域条件
/// </summary>
public class SearchArea
{
    public int TopLeftLongitude { get; set; }
    public int TopLeftLatitude { get; set; }
    public int BottomRightLongitude { get; set; }
    public int BottomRightLatitude { get; set; }
}

/// <summary>
/// 文件下载路径配置
/// </summary>
public class FileDownloadPath
{
    public string LocalPath { get; set; } = string.Empty;
    public string RemotePath { get; set; } = string.Empty;
}

/// <summary>
/// 更新描述信息
/// </summary>
public class UpdateDescription
{
    public int Version { get; set; }
    public int Time { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 进程信息
/// </summary>
public class ProcessInfo
{
    public string ExecutableName { get; set; } = string.Empty;
    public int ProcessId { get; set; } = -1;

    public void Clear()
    {
        ExecutableName = string.Empty;
        ProcessId = -1;
    }
}
