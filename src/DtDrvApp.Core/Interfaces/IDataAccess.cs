using DtDrvApp.Core.Entities;

namespace DtDrvApp.Core.Interfaces;

/// <summary>
/// 数据访问接口 - 对应C++中的CDataBaseDeal
/// </summary>
public interface IDataAccess
{
    /// <summary>
    /// 设置数据库连接参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    void SetConnectionInfo(DatabaseConnectionInfo connectionInfo);

    /// <summary>
    /// 执行SQL语句
    /// </summary>
    /// <param name="sql">SQL语句</param>
    /// <param name="parameters">参数</param>
    /// <param name="displayLog">是否显示日志</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>影响的行数</returns>
    Task<int> ExecuteSqlAsync(string sql, object? parameters = null, bool displayLog = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询SQL并返回结果
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="sql">SQL语句</param>
    /// <param name="parameters">参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>查询结果</returns>
    Task<IEnumerable<T>> QueryAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询单个结果
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="sql">SQL语句</param>
    /// <param name="parameters">参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>查询结果</returns>
    Task<T?> QuerySingleOrDefaultAsync<T>(string sql, object? parameters = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量导入数据文件
    /// </summary>
    /// <param name="filename">文件名</param>
    /// <param name="tablename">表名</param>
    /// <param name="deleteMode">是否删除文件</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task BulkCopyDataFileAsync(string filename, string tablename, bool deleteMode = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查数据库连接是否正常
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否正常</returns>
    Task<bool> IsConnectionNormalAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取连接信息
    /// </summary>
    /// <returns>连接信息</returns>
    DatabaseConnectionInfo? GetConnectionInfo();
}

/// <summary>
/// 查询记录处理接口
/// </summary>
public interface IQueryRecordHandler
{
    /// <summary>
    /// 处理查询结果行
    /// </summary>
    /// <param name="queryType">查询类型</param>
    /// <returns>是否成功</returns>
    Task<bool> HandleRowResultAsync(int queryType);

    /// <summary>
    /// 清理资源
    /// </summary>
    void Clear();
}

/// <summary>
/// 数据库信息管理接口
/// </summary>
public interface IDatabaseInfoManager
{
    /// <summary>
    /// 获取用户状态
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户状态</returns>
    Task<UserStatus?> GetUserStatusAsync(int userId);

    /// <summary>
    /// 检查数据库连接信息是否存在
    /// </summary>
    /// <param name="databaseId">数据库ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsDbConnectionInfoExistAsync(int databaseId);

    /// <summary>
    /// 根据项目ID获取测试类型
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>测试类型</returns>
    Task<TestType> GetTestTypeByProjectIdAsync(int projectId);

    /// <summary>
    /// 获取密码失效天数
    /// </summary>
    /// <returns>天数</returns>
    Task<int> GetPasswordInvalidDaysAsync();

    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> ReadDataAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 设置数据库连接信息
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    void SetDatabaseConnectionInfo(DatabaseConnectionInfo connectionInfo);

    /// <summary>
    /// 获取数据库连接信息
    /// </summary>
    /// <param name="databaseId">数据库ID</param>
    /// <returns>连接信息</returns>
    Task<DatabaseConnectionInfo?> GetDatabaseConnectionInfoAsync(int databaseId);
}
