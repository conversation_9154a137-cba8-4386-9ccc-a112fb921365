using DtDrvApp.Core.Entities;

namespace DtDrvApp.Core.Interfaces;

/// <summary>
/// 网络服务接口 - 对应C++中的PPM_Tcp_Server
/// </summary>
public interface INetworkService
{
    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端连接数
    /// </summary>
    /// <returns>连接数</returns>
    int GetClientCount();

    /// <summary>
    /// 获取客户端连接数峰值
    /// </summary>
    /// <returns>峰值连接数</returns>
    int GetMaxClientCount();

    /// <summary>
    /// 重置客户端连接数峰值
    /// </summary>
    void ResetClientCount();

    /// <summary>
    /// 是否支持IPv6
    /// </summary>
    bool IsIPv6Enabled { get; }

    /// <summary>
    /// 服务端口
    /// </summary>
    int Port { get; }
}

/// <summary>
/// Socket连接处理器接口 - 对应C++中的CDtSockServer
/// </summary>
public interface ISocketHandler
{
    /// <summary>
    /// 处理数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="length">数据长度</param>
    /// <param name="socketInfo">Socket信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> HandleDataAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 开始处理连接
    /// </summary>
    /// <param name="socketInfo">Socket信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task BeginHandleAsync(SocketInfo socketInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="length">数据长度</param>
    /// <param name="socketInfo">Socket信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送的字节数</returns>
    Task<int> SendDataAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default);
}

/// <summary>
/// 认证处理接口
/// </summary>
public interface IAuthenticationHandler
{
    /// <summary>
    /// 初始化认证处理器
    /// </summary>
    /// <param name="dataAccess">数据访问</param>
    /// <param name="socketHandler">Socket处理器</param>
    void Initialize(IDataAccess dataAccess, ISocketHandler socketHandler);

    /// <summary>
    /// 处理认证 - 兼容旧版本
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="length">数据长度</param>
    /// <param name="socketInfo">Socket信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> HandleAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理用户名认证 - 对应C++中的AuthenDealUser
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="length">数据长度</param>
    /// <param name="socketInfo">Socket信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> HandleUsernameAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理密码认证 - 对应C++中的AuthenDealPassWord
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="length">数据长度</param>
    /// <param name="socketInfo">Socket信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> HandlePasswordAuthenticationAsync(byte[] data, int length, SocketInfo socketInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>随机字符串</returns>
    string GenerateRandomString(int length = 30);
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public UserInfo? UserInfo { get; set; }
    public byte[]? ResponseData { get; set; }
}

/// <summary>
/// 网络缓冲区接口 - 对应C++中的PPM_SOCK_Buffer
/// </summary>
public interface INetworkBuffer
{
    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="size">大小</param>
    /// <returns>读取的字节数</returns>
    Task<int> ReadDataAsync(byte[] buffer, int size);

    /// <summary>
    /// 写入数据
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="size">大小</param>
    /// <returns>写入的字节数</returns>
    Task<int> WriteDataAsync(byte[] buffer, int size);

    /// <summary>
    /// 获取发送数据
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="count">数量</param>
    /// <returns>获取的字节数</returns>
    Task<int> GetSendDataAsync(byte[] buffer, int count);

    /// <summary>
    /// 放入接收数据
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="count">数量</param>
    /// <returns>放入的字节数</returns>
    Task<int> PutReceiveDataAsync(byte[] buffer, int count);

    /// <summary>
    /// 获取忙碌大小
    /// </summary>
    /// <returns>忙碌大小</returns>
    int GetBusySize();

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    string ClientIpAddress { get; set; }

    /// <summary>
    /// 客户端端口号
    /// </summary>
    int ClientPort { get; set; }
}
