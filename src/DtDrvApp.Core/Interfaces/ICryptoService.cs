namespace DtDrvApp.Core.Interfaces;

/// <summary>
/// 加密服务接口 - 对应C++中的CDESCrypto
/// </summary>
public interface ICryptoService
{
    /// <summary>
    /// DES加密
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="mode">加密模式</param>
    /// <param name="paddingMode">填充模式</param>
    /// <returns>加密后的Base64字符串</returns>
    string DesEncrypt(string plainText, string key, CipherMode mode = CipherMode.ECB, PaddingMode paddingMode = PaddingMode.PKCS7);

    /// <summary>
    /// DES解密
    /// </summary>
    /// <param name="cipherText">密文（Base64字符串）</param>
    /// <param name="key">密钥</param>
    /// <param name="mode">加密模式</param>
    /// <param name="paddingMode">填充模式</param>
    /// <returns>解密后的明文</returns>
    string DesDecrypt(string cipherText, string key, CipherMode mode = CipherMode.ECB, PaddingMode paddingMode = PaddingMode.PKCS7);

    /// <summary>
    /// 3DES加密
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="mode">加密模式</param>
    /// <param name="paddingMode">填充模式</param>
    /// <returns>加密后的Base64字符串</returns>
    string TripleDesEncrypt(string plainText, string key, CipherMode mode = CipherMode.ECB, PaddingMode paddingMode = PaddingMode.PKCS7);

    /// <summary>
    /// 3DES解密
    /// </summary>
    /// <param name="cipherText">密文（Base64字符串）</param>
    /// <param name="key">密钥</param>
    /// <param name="mode">加密模式</param>
    /// <param name="paddingMode">填充模式</param>
    /// <returns>解密后的明文</returns>
    string TripleDesDecrypt(string cipherText, string key, CipherMode mode = CipherMode.ECB, PaddingMode paddingMode = PaddingMode.PKCS7);

    /// <summary>
    /// Base64编码
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>Base64字符串</returns>
    string Base64Encode(byte[] data);

    /// <summary>
    /// Base64编码
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <returns>Base64字符串</returns>
    string Base64Encode(string text);

    /// <summary>
    /// Base64解码
    /// </summary>
    /// <param name="base64Text">Base64字符串</param>
    /// <returns>原始数据</returns>
    byte[] Base64Decode(string base64Text);

    /// <summary>
    /// Base64解码为字符串
    /// </summary>
    /// <param name="base64Text">Base64字符串</param>
    /// <returns>原始文本</returns>
    string Base64DecodeToString(string base64Text);

    /// <summary>
    /// MD5哈希
    /// </summary>
    /// <param name="input">输入文本</param>
    /// <returns>MD5哈希值（十六进制字符串）</returns>
    string Md5Hash(string input);

    /// <summary>
    /// MD5哈希
    /// </summary>
    /// <param name="data">输入数据</param>
    /// <returns>MD5哈希值（十六进制字符串）</returns>
    string Md5Hash(byte[] data);

    /// <summary>
    /// SHA256哈希
    /// </summary>
    /// <param name="input">输入文本</param>
    /// <returns>SHA256哈希值（十六进制字符串）</returns>
    string Sha256Hash(string input);

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    /// <param name="length">密钥长度</param>
    /// <returns>随机密钥</returns>
    string GenerateRandomKey(int length = 8);

    /// <summary>
    /// 生成随机IV
    /// </summary>
    /// <param name="length">IV长度</param>
    /// <returns>随机IV</returns>
    byte[] GenerateRandomIV(int length = 8);
}

/// <summary>
/// 加密模式枚举 - 对应C++中的CipherMode
/// </summary>
public enum CipherMode
{
    /// <summary>
    /// 通用模式
    /// </summary>
    General = 0,

    /// <summary>
    /// ECB模式
    /// </summary>
    ECB = 1,

    /// <summary>
    /// CBC模式
    /// </summary>
    CBC = 2,

    /// <summary>
    /// CFB模式
    /// </summary>
    CFB = 3,

    /// <summary>
    /// 3DES ECB模式
    /// </summary>
    TripleECB = 4,

    /// <summary>
    /// 3DES CBC模式
    /// </summary>
    TripleCBC = 5
}

/// <summary>
/// 填充模式枚举 - 对应C++中的PaddingMode
/// </summary>
public enum PaddingMode
{
    /// <summary>
    /// 无填充
    /// </summary>
    None = 0,

    /// <summary>
    /// PKCS7填充
    /// </summary>
    PKCS7 = 1,

    /// <summary>
    /// 零填充
    /// </summary>
    Zeros = 2,

    /// <summary>
    /// ANSIX923填充
    /// </summary>
    ANSIX923 = 3,

    /// <summary>
    /// ISO10126填充
    /// </summary>
    ISO10126 = 4
}
