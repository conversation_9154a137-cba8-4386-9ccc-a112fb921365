using DtDrvApp.Core.Entities;

namespace DtDrvApp.Core.Interfaces;

/// <summary>
/// 命令处理器接口
/// </summary>
public interface ICommandHandler
{
    /// <summary>
    /// 命令ID
    /// </summary>
    byte CommandId { get; }

    /// <summary>
    /// 处理命令
    /// </summary>
    /// <param name="request">命令请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>命令响应</returns>
    Task<CommandResponse> HandleAsync(CommandRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// 命令请求
/// </summary>
public class CommandRequest
{
    public byte CommandId { get; set; }
    public byte SubCommandId { get; set; }
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public int DataLength { get; set; }
    public SocketInfo SocketInfo { get; set; } = new();
    public DateTime RequestTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 命令响应
/// </summary>
public class CommandResponse
{
    public bool Success { get; set; }
    public byte ResponseType { get; set; }
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string? ErrorMessage { get; set; }
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 命令分发器接口
/// </summary>
public interface ICommandDispatcher
{
    /// <summary>
    /// 分发命令
    /// </summary>
    /// <param name="commandId">命令ID</param>
    /// <param name="request">请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应</returns>
    Task<CommandResponse> DispatchAsync(byte commandId, CommandRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册命令处理器
    /// </summary>
    /// <param name="handler">处理器</param>
    void RegisterHandler(ICommandHandler handler);

    /// <summary>
    /// 获取所有已注册的命令处理器
    /// </summary>
    /// <returns>命令处理器列表</returns>
    IEnumerable<ICommandHandler> GetRegisteredHandlers();
}
