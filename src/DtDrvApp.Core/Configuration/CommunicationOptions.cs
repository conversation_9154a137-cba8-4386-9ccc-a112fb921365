namespace DtDrvApp.Core.Configuration;

/// <summary>
/// 通信配置选项 - 对应C++配置文件中的[COMM]节
/// </summary>
public class CommunicationOptions
{
    public const string SectionName = "Communication";

    /// <summary>
    /// 数据端口
    /// </summary>
    public int DataPort { get; set; } = 23456;

    /// <summary>
    /// 是否启用IPv6
    /// </summary>
    public bool IsIPv6Enable { get; set; } = false;

    /// <summary>
    /// 客户端日志记录
    /// </summary>
    public bool ClientLogger { get; set; } = false;

    /// <summary>
    /// 加密密钥
    /// </summary>
    public string Key { get; set; } = "b374e04c3480d9cb17dd5c0f02dbee66";

    /// <summary>
    /// 客户端路径
    /// </summary>
    public string ClientPath { get; set; } = string.Empty;

    /// <summary>
    /// CQT图片临时路径
    /// </summary>
    public string CqtImageTemperPath { get; set; } = string.Empty;

    /// <summary>
    /// XLSX临时路径
    /// </summary>
    public string XlsxTemperPath { get; set; } = string.Empty;

    /// <summary>
    /// MCC文件路径
    /// </summary>
    public string MccFilePath { get; set; } = string.Empty;

    /// <summary>
    /// 强制更新标志
    /// </summary>
    public bool ForciblyUpdate { get; set; } = false;
}

/// <summary>
/// 数据库配置选项 - 对应C++配置文件中的[DBSETTING]节
/// </summary>
public class DatabaseOptions
{
    public const string SectionName = "DatabaseSettings";

    /// <summary>
    /// 是否显示SQL
    /// </summary>
    public bool DisplaySql { get; set; } = true;

    /// <summary>
    /// 命令超时时间（秒）
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// 最大连接池大小
    /// </summary>
    public int MaxPoolSize { get; set; } = 100;

    /// <summary>
    /// 最小连接池大小
    /// </summary>
    public int MinPoolSize { get; set; } = 5;

    /// <summary>
    /// 连接重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 连接重试间隔（毫秒）
    /// </summary>
    public int RetryInterval { get; set; } = 1000;
}

/// <summary>
/// 文件下载路径配置 - 对应C++配置文件中的[FILEDOWNLOADPATH]节
/// </summary>
public class FileDownloadOptions
{
    public const string SectionName = "FileDownload";

    /// <summary>
    /// 文件路径组数量
    /// </summary>
    public int FilePathGroup { get; set; } = 0;

    /// <summary>
    /// 文件下载路径列表
    /// </summary>
    public List<FileDownloadPath> Paths { get; set; } = new();
}

/// <summary>
/// 文件下载路径
/// </summary>
public class FileDownloadPath
{
    /// <summary>
    /// 本地路径
    /// </summary>
    public string LocalPath { get; set; } = string.Empty;

    /// <summary>
    /// 远程路径
    /// </summary>
    public string RemotePath { get; set; } = string.Empty;

    /// <summary>
    /// 路径索引
    /// </summary>
    public string PathIndex { get; set; } = string.Empty;
}

/// <summary>
/// 重启设置配置 - 对应C++配置文件中的[ReStartSetting]节
/// </summary>
public class RestartOptions
{
    public const string SectionName = "RestartSettings";

    /// <summary>
    /// 是否需要重启
    /// </summary>
    public bool NeedRestart { get; set; } = false;

    /// <summary>
    /// 重启时间 - 小时
    /// </summary>
    public int Hour { get; set; } = 0;

    /// <summary>
    /// 重启时间 - 分钟
    /// </summary>
    public int Minute { get; set; } = 0;

    /// <summary>
    /// 重启时间 - 秒
    /// </summary>
    public int Second { get; set; } = 0;
}

/// <summary>
/// 日志配置选项 - 对应C++配置文件中的[LOGLEVEL]和[LOGOUTPUT]节
/// </summary>
public class LoggingOptions
{
    public const string SectionName = "Logging";

    /// <summary>
    /// 日志级别设置
    /// </summary>
    public LogLevelSettings LogLevel { get; set; } = new();

    /// <summary>
    /// 日志输出设置
    /// </summary>
    public LogOutputSettings LogOutput { get; set; } = new();

    /// <summary>
    /// 日志文件设置
    /// </summary>
    public LogFileSettings LogFile { get; set; } = new();
}

/// <summary>
/// 日志级别设置
/// </summary>
public class LogLevelSettings
{
    public bool Debug { get; set; } = true;
    public bool Info { get; set; } = true;
    public bool Warning { get; set; } = true;
    public bool Error { get; set; } = true;
    public bool Notice { get; set; } = false;
}

/// <summary>
/// 日志输出设置
/// </summary>
public class LogOutputSettings
{
    public bool Console { get; set; } = true;
    public bool File { get; set; } = true;
    public bool Callback { get; set; } = true;
}

/// <summary>
/// 日志文件设置
/// </summary>
public class LogFileSettings
{
    /// <summary>
    /// 日志文件路径
    /// </summary>
    public string Path { get; set; } = "logs";

    /// <summary>
    /// 日志文件名前缀
    /// </summary>
    public string FileNamePrefix { get; set; } = "dtdrvapp";

    /// <summary>
    /// 最大文件大小（MB）
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 5;

    /// <summary>
    /// 保留文件天数
    /// </summary>
    public int RetainedFileCountLimit { get; set; } = 7;

    /// <summary>
    /// 是否按日期滚动
    /// </summary>
    public bool RollOnFileSizeLimit { get; set; } = true;
}
