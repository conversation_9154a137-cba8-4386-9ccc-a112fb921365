using Microsoft.Extensions.Logging;

namespace DtDrvApp.Core.Extensions;

/// <summary>
/// 日志扩展方法 - 对应C++中的PPM_Log_Msg功能
/// </summary>
public static class LoggerExtensions
{
    // 定义日志事件ID
    private static readonly EventId SqlExecutionEventId = new(1001, "SqlExecution");
    private static readonly EventId SocketOperationEventId = new(1002, "SocketOperation");
    private static readonly EventId AuthenticationEventId = new(1003, "Authentication");
    private static readonly EventId CommandProcessingEventId = new(1004, "CommandProcessing");
    private static readonly EventId DatabaseConnectionEventId = new(1005, "DatabaseConnection");
    private static readonly EventId UserOperationEventId = new(1006, "UserOperation");
    private static readonly EventId SystemOperationEventId = new(1007, "SystemOperation");
    private static readonly EventId PerformanceEventId = new(1008, "Performance");

    /// <summary>
    /// 记录SQL执行日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="sql">SQL语句</param>
    /// <param name="parameters">参数</param>
    /// <param name="executionTime">执行时间（毫秒）</param>
    /// <param name="affectedRows">影响行数</param>
    public static void LogSqlExecution(this ILogger logger, string sql, object? parameters = null, 
        long executionTime = 0, int affectedRows = 0)
    {
        if (logger.IsEnabled(LogLevel.Debug))
        {
            logger.LogDebug(SqlExecutionEventId, 
                "SQL executed in {ExecutionTime}ms, affected {AffectedRows} rows: {Sql} {@Parameters}",
                executionTime, affectedRows, sql, parameters);
        }
    }

    /// <summary>
    /// 记录SQL执行错误
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="exception">异常</param>
    /// <param name="sql">SQL语句</param>
    /// <param name="parameters">参数</param>
    public static void LogSqlError(this ILogger logger, Exception exception, string sql, object? parameters = null)
    {
        logger.LogError(SqlExecutionEventId, exception,
            "SQL execution failed: {Sql} {@Parameters}", sql, parameters);
    }

    /// <summary>
    /// 记录Socket操作日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="operation">操作类型</param>
    /// <param name="clientInfo">客户端信息</param>
    /// <param name="dataLength">数据长度</param>
    /// <param name="success">是否成功</param>
    public static void LogSocketOperation(this ILogger logger, string operation, string clientInfo, 
        int dataLength = 0, bool success = true)
    {
        if (success)
        {
            logger.LogInformation(SocketOperationEventId,
                "Socket {Operation} successful for client {ClientInfo}, data length: {DataLength}",
                operation, clientInfo, dataLength);
        }
        else
        {
            logger.LogWarning(SocketOperationEventId,
                "Socket {Operation} failed for client {ClientInfo}, data length: {DataLength}",
                operation, clientInfo, dataLength);
        }
    }

    /// <summary>
    /// 记录认证日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="username">用户名</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="success">是否成功</param>
    /// <param name="step">认证步骤</param>
    /// <param name="errorMessage">错误消息</param>
    public static void LogAuthentication(this ILogger logger, string username, string clientIp, 
        bool success, int step = 0, string? errorMessage = null)
    {
        if (success)
        {
            logger.LogInformation(AuthenticationEventId,
                "Authentication successful for user {Username} from {ClientIp}, step: {Step}",
                username, clientIp, step);
        }
        else
        {
            logger.LogWarning(AuthenticationEventId,
                "Authentication failed for user {Username} from {ClientIp}, step: {Step}, error: {Error}",
                username, clientIp, step, errorMessage);
        }
    }

    /// <summary>
    /// 记录命令处理日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="commandId">命令ID</param>
    /// <param name="clientInfo">客户端信息</param>
    /// <param name="processingTime">处理时间（毫秒）</param>
    /// <param name="success">是否成功</param>
    /// <param name="errorMessage">错误消息</param>
    public static void LogCommandProcessing(this ILogger logger, byte commandId, string clientInfo, 
        long processingTime = 0, bool success = true, string? errorMessage = null)
    {
        if (success)
        {
            logger.LogInformation(CommandProcessingEventId,
                "Command {CommandId:X2} processed successfully for client {ClientInfo} in {ProcessingTime}ms",
                commandId, clientInfo, processingTime);
        }
        else
        {
            logger.LogError(CommandProcessingEventId,
                "Command {CommandId:X2} processing failed for client {ClientInfo} in {ProcessingTime}ms: {Error}",
                commandId, clientInfo, processingTime, errorMessage);
        }
    }

    /// <summary>
    /// 记录数据库连接日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="server">服务器地址</param>
    /// <param name="database">数据库名</param>
    /// <param name="success">是否成功</param>
    /// <param name="connectionTime">连接时间（毫秒）</param>
    /// <param name="errorMessage">错误消息</param>
    public static void LogDatabaseConnection(this ILogger logger, string server, string database, 
        bool success, long connectionTime = 0, string? errorMessage = null)
    {
        if (success)
        {
            logger.LogDebug(DatabaseConnectionEventId,
                "Database connection successful to {Server}/{Database} in {ConnectionTime}ms",
                server, database, connectionTime);
        }
        else
        {
            logger.LogError(DatabaseConnectionEventId,
                "Database connection failed to {Server}/{Database} in {ConnectionTime}ms: {Error}",
                server, database, connectionTime, errorMessage);
        }
    }

    /// <summary>
    /// 记录用户操作日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="username">用户名</param>
    /// <param name="operation">操作</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="details">操作详情</param>
    public static void LogUserOperation(this ILogger logger, string username, string operation, 
        string clientIp, string? details = null)
    {
        logger.LogInformation(UserOperationEventId,
            "User {Username} performed {Operation} from {ClientIp}: {Details}",
            username, operation, clientIp, details ?? "N/A");
    }

    /// <summary>
    /// 记录系统操作日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="operation">操作</param>
    /// <param name="component">组件</param>
    /// <param name="details">详情</param>
    /// <param name="success">是否成功</param>
    public static void LogSystemOperation(this ILogger logger, string operation, string component, 
        string? details = null, bool success = true)
    {
        if (success)
        {
            logger.LogInformation(SystemOperationEventId,
                "System operation {Operation} completed successfully in component {Component}: {Details}",
                operation, component, details ?? "N/A");
        }
        else
        {
            logger.LogError(SystemOperationEventId,
                "System operation {Operation} failed in component {Component}: {Details}",
                operation, component, details ?? "N/A");
        }
    }

    /// <summary>
    /// 记录性能指标
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="metric">指标名称</param>
    /// <param name="value">指标值</param>
    /// <param name="unit">单位</param>
    /// <param name="context">上下文</param>
    public static void LogPerformanceMetric(this ILogger logger, string metric, double value, 
        string unit = "ms", string? context = null)
    {
        logger.LogInformation(PerformanceEventId,
            "Performance metric {Metric}: {Value} {Unit} {Context}",
            metric, value, unit, context ?? "");
    }

    /// <summary>
    /// 记录应用程序启动日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="applicationName">应用程序名称</param>
    /// <param name="version">版本</param>
    /// <param name="environment">环境</param>
    public static void LogApplicationStart(this ILogger logger, string applicationName, 
        string version, string environment)
    {
        logger.LogInformation(
            "Application {ApplicationName} v{Version} starting in {Environment} environment",
            applicationName, version, environment);
    }

    /// <summary>
    /// 记录应用程序停止日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="applicationName">应用程序名称</param>
    /// <param name="uptime">运行时间</param>
    public static void LogApplicationStop(this ILogger logger, string applicationName, TimeSpan uptime)
    {
        logger.LogInformation(
            "Application {ApplicationName} stopping after running for {Uptime}",
            applicationName, uptime);
    }

    /// <summary>
    /// 记录配置变更日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configKey">配置键</param>
    /// <param name="oldValue">旧值</param>
    /// <param name="newValue">新值</param>
    /// <param name="username">操作用户</param>
    public static void LogConfigurationChange(this ILogger logger, string configKey, 
        string? oldValue, string? newValue, string username)
    {
        logger.LogInformation(
            "Configuration changed by {Username}: {ConfigKey} changed from '{OldValue}' to '{NewValue}'",
            username, configKey, oldValue ?? "null", newValue ?? "null");
    }

    /// <summary>
    /// 记录资源使用情况
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="resourceType">资源类型</param>
    /// <param name="used">已使用</param>
    /// <param name="total">总量</param>
    /// <param name="unit">单位</param>
    public static void LogResourceUsage(this ILogger logger, string resourceType, 
        long used, long total, string unit = "bytes")
    {
        var percentage = total > 0 ? (double)used / total * 100 : 0;
        
        logger.LogDebug(PerformanceEventId,
            "Resource usage - {ResourceType}: {Used}/{Total} {Unit} ({Percentage:F1}%)",
            resourceType, used, total, unit, percentage);
    }

    /// <summary>
    /// 记录批量操作日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="operation">操作类型</param>
    /// <param name="itemCount">项目数量</param>
    /// <param name="successCount">成功数量</param>
    /// <param name="failureCount">失败数量</param>
    /// <param name="duration">持续时间</param>
    public static void LogBatchOperation(this ILogger logger, string operation, 
        int itemCount, int successCount, int failureCount, TimeSpan duration)
    {
        logger.LogInformation(
            "Batch {Operation} completed: {ItemCount} items processed, {SuccessCount} succeeded, " +
            "{FailureCount} failed in {Duration}",
            operation, itemCount, successCount, failureCount, duration);
    }
}
