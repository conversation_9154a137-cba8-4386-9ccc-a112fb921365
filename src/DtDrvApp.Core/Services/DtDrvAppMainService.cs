using DtDrvApp.Core.Configuration;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DtDrvApp.Core.Services;

/// <summary>
/// DtDrvApp主服务类 - 对应C++中的CDtDrvAppMain
/// </summary>
public class DtDrvAppMainService : BackgroundService
{
    private readonly ILogger<DtDrvAppMainService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly INetworkService _networkService;
    private readonly IOptions<CommunicationOptions> _communicationOptions;
    private readonly IOptions<DatabaseOptions> _databaseOptions;
    
    private bool _isInitialized = false;
    private int _lastDay = 0;
    private readonly object _lockObject = new();

    public DtDrvAppMainService(
        ILogger<DtDrvAppMainService> logger,
        IServiceProvider serviceProvider,
        INetworkService networkService,
        IOptions<CommunicationOptions> communicationOptions,
        IOptions<DatabaseOptions> databaseOptions)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _networkService = networkService;
        _communicationOptions = communicationOptions;
        _databaseOptions = databaseOptions;
    }

    /// <summary>
    /// 执行主循环 - 对应C++中的Main()方法
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("*****************************************************");
            _logger.LogInformation("***** WELCOME TO LOGIN DtDrvApp Decode PROGRAM ******");
            _logger.LogInformation("*****************************************************");

            // 检查是否已有实例在运行
            if (await IsProcessRunningAsync())
            {
                _logger.LogError("Program already exists! Program will exit in 2 seconds!");
                try
                {
                    await Task.Delay(2000, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Shutdown requested during startup delay");
                }
                return;
            }

            // 初始化
            if (!_isInitialized)
            {
                _isInitialized = true;

                // 初始化日志
                if (!await InitializeLogAsync())
                {
                    await CloseProgram();
                    return;
                }

                // 主处理对象初始化
                if (!await InitializeAsync())
                {
                    await CloseProgram();
                    return;
                }

                _logger.LogInformation("DtDrvApp initialization completed successfully");
            }

            // 主循环
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 检查配置有效性
                    if (!IsConfigurationValid())
                    {
                        try
                        {
                            await Task.Delay(1000, stoppingToken);
                        }
                        catch (OperationCanceledException)
                        {
                            _logger.LogInformation("Configuration check delay cancelled, exiting");
                            break;
                        }
                        continue;
                    }

                    // 在凌晨2点进行数据压缩等维护任务
                    await PerformMaintenanceTasksAsync();

                    // 看门狗检查
                    await WatchDogAsync();

                    await Task.Delay(1000, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，退出循环
                    _logger.LogInformation("Main loop cancelled, exiting gracefully");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in main execution loop");

                    // 出错后等待5秒再继续，使用带超时的取消检查
                    if (!stoppingToken.IsCancellationRequested)
                    {
                        try
                        {
                            // 创建一个组合的取消令牌：要么5秒超时，要么应用程序关闭
                            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
                            timeoutCts.CancelAfter(System.TimeSpan.FromSeconds(5));
                            await Task.Delay(Timeout.Infinite, timeoutCts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            // 可能是超时（正常）或应用程序关闭
                            if (stoppingToken.IsCancellationRequested)
                            {
                                _logger.LogInformation("Application shutdown requested during error recovery, exiting");
                                break;
                            }
                            // 否则是超时，继续循环
                        }
                    }
                    else
                    {
                        // 应用程序正在关闭，直接退出
                        break;
                    }
                }
            }

            _logger.LogInformation("DtDrvApp Stop...");
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Critical error in DtDrvApp main service");
            throw;
        }
    }

    /// <summary>
    /// 初始化日志 - 对应C++中的Init_Log()
    /// </summary>
    private async Task<bool> InitializeLogAsync()
    {
        try
        {
            _logger.LogInformation("Initializing logging system...");
            // 日志系统已通过依赖注入配置，这里可以进行额外的初始化
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize logging system");
            return false;
        }
    }

    /// <summary>
    /// 初始化 - 对应C++中的Init()
    /// </summary>
    private async Task<bool> InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Starting DtDrvApp initialization...");

            // 初始化数据库信息管理器
            using (var scope = _serviceProvider.CreateScope())
            {
                var databaseInfoManager = scope.ServiceProvider.GetRequiredService<IDatabaseInfoManager>();
                if (!await databaseInfoManager.ReadDataAsync())
                {
                    _logger.LogError("Failed to initialize database info manager");
                    return false;
                }
            }

            // 初始化Socket服务器
            if (!await InitializeSocketServerAsync())
            {
                _logger.LogError("Failed to initialize socket server");
                return false;
            }

            // 初始化监控模块
            if (!await InitializeMonitorAsync())
            {
                _logger.LogError("Failed to initialize monitor");
                return false;
            }

            _logger.LogInformation("DtDrvApp initialization completed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during initialization");
            return false;
        }
    }

    /// <summary>
    /// 初始化Socket服务器 - 对应C++中的Init_Sock_Server()
    /// </summary>
    private async Task<bool> InitializeSocketServerAsync()
    {
        try
        {
            _logger.LogInformation("Initializing socket server on port {Port}, IPv6: {IPv6Enabled}",
                _networkService.Port, _networkService.IsIPv6Enabled);

            // 检查端口是否可用
            if (!IsPortAvailable(_networkService.Port))
            {
                _logger.LogError("Port {Port} is already in use by another process", _networkService.Port);
                return false;
            }

            await _networkService.StartAsync();

            _logger.LogInformation("Socket server started successfully");
            return true;
        }
        catch (System.Net.Sockets.SocketException sockEx)
        {
            _logger.LogError(sockEx, "Socket error during server initialization. Error code: {ErrorCode}, Message: {Message}",
                sockEx.ErrorCode, sockEx.Message);

            if (sockEx.ErrorCode == 10048 || sockEx.ErrorCode == 98) // WSAEADDRINUSE or EADDRINUSE
            {
                _logger.LogError("Port {Port} is already in use. Please check if another instance is running or change the port.",
                    _networkService.Port);
            }

            return false;
        }
        catch (UnauthorizedAccessException authEx)
        {
            _logger.LogError(authEx, "Access denied when trying to bind to port {Port}. May require administrator privileges.",
                _networkService.Port);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during socket server initialization: {ExceptionType}",
                ex.GetType().Name);
            return false;
        }
    }

    /// <summary>
    /// 检查端口是否可用
    /// </summary>
    private bool IsPortAvailable(int port)
    {
        try
        {
            // 检查 TCP 端口
            using var tcpListener = new System.Net.Sockets.TcpListener(System.Net.IPAddress.Any, port);
            tcpListener.Start();
            tcpListener.Stop();

            // 额外等待一小段时间，确保端口完全释放
            System.Threading.Thread.Sleep(100);

            _logger.LogDebug("Port {Port} is available", port);
            return true;
        }
        catch (System.Net.Sockets.SocketException sockEx)
        {
            _logger.LogWarning("Port {Port} is not available. Socket error: {ErrorCode} - {Message}",
                port, sockEx.ErrorCode, sockEx.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Unexpected error while checking port {Port} availability", port);
            return false;
        }
    }

    /// <summary>
    /// 初始化监控模块 - 对应C++中的Init_Monitor()
    /// </summary>
    private async Task<bool> InitializeMonitorAsync()
    {
        try
        {
            _logger.LogInformation("Initializing monitor module...");
            // 这里可以初始化自监控模块
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize monitor module");
            return false;
        }
    }

    /// <summary>
    /// 检查是否已有进程在运行
    /// </summary>
    private async Task<bool> IsProcessRunningAsync()
    {
        try
        {
            // 这里可以实现进程检查逻辑
            // 在.NET中可以使用Process.GetProcessesByName()等方法
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if process is running");
            return false;
        }
    }

    /// <summary>
    /// 检查配置有效性
    /// </summary>
    private bool IsConfigurationValid()
    {
        // 这里可以添加配置验证逻辑
        return true;
    }

    /// <summary>
    /// 执行维护任务
    /// </summary>
    private async Task PerformMaintenanceTasksAsync()
    {
        var now = DateTime.Now;
        
        // 在凌晨2点执行维护任务
        if (now.Hour == 2 && _lastDay != now.Day)
        {
            lock (_lockObject)
            {
                if (_lastDay != now.Day)
                {
                    _lastDay = now.Day;
                    _logger.LogInformation("Performing daily maintenance tasks...");
                    
                    // 这里可以添加数据压缩、清理等维护任务
                    // 例如：压缩前7天的数据等
                }
            }
        }
    }

    /// <summary>
    /// 看门狗检查 - 对应C++中的WatchDog()
    /// </summary>
    private async Task<bool> WatchDogAsync()
    {
        try
        {
            // 这里可以实现看门狗逻辑
            // 检查各个服务的健康状态
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in watchdog check");
            return false;
        }
    }

    /// <summary>
    /// 关闭程序
    /// </summary>
    private async Task CloseProgram()
    {
        try
        {
            _logger.LogInformation("Closing DtDrvApp...");
            
            // 停止网络服务
            await _networkService.StopAsync();
            
            _logger.LogInformation("DtDrvApp closed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during program shutdown");
        }
    }

    /// <summary>
    /// 获取客户端连接数
    /// </summary>
    public int GetClientCount()
    {
        return _networkService.GetClientCount();
    }

    /// <summary>
    /// 获取客户端连接数峰值
    /// </summary>
    public int GetMaxClientCount()
    {
        return _networkService.GetMaxClientCount();
    }

    /// <summary>
    /// 重置客户端连接数峰值
    /// </summary>
    public void ResetClientCount()
    {
        _networkService.ResetClientCount();
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("DtDrvApp service is stopping...");
        await CloseProgram();
        await base.StopAsync(cancellationToken);
    }
}
