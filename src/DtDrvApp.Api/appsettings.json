{"Communication": {"DataPort": 23456, "IsIPv6Enable": false, "ClientLogger": false, "Key": "b374e04c3480d9cb17dd5c0f02dbee66", "ClientPath": "", "CqtImageTemperPath": "", "XlsxTemperPath": "", "MccFilePath": "", "ForciblyUpdate": false}, "ConnectionStrings": {"DefaultConnection": "Server=************,1433;Database=DTASERVER;User Id=dtauser;Password=dtauser;TrustServerCertificate=true;", "MySqlConnection": "Server=localhost;Port=3306;Database=DtDrvApp;Uid=dtdrvapp_user;Pwd=your_password_here;"}, "DatabaseSettings": {"DisplaySql": true, "CommandTimeout": 30, "ConnectionTimeout": 30, "MaxPoolSize": 100, "MinPoolSize": 5, "RetryCount": 3, "RetryInterval": 1000}, "FileDownload": {"FilePathGroup": 0, "Paths": []}, "RestartSettings": {"NeedRestart": false, "Hour": 0, "Minute": 0, "Second": 0}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "DtDrvApp": "Debug"}, "LogOutput": {"Console": true, "File": true, "Callback": true}, "LogFile": {"Path": "logs", "FileNamePrefix": "dtdrvapp", "MaxFileSizeMB": 5, "RetainedFileCountLimit": 7, "RollOnFileSizeLimit": true}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning", "DtDrvApp": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/dtdrvapp-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 5242880, "rollOnFileSizeLimit": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/dtdrvapp-sql-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 5242880, "rollOnFileSizeLimit": true, "restrictedToMinimumLevel": "Debug", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}, "Https": {"Url": "https://localhost:5001"}}}}