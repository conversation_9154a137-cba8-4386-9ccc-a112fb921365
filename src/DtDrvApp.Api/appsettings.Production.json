{"Communication": {"DataPort": 23456, "IsIPv6Enable": false, "ClientLogger": false, "Key": "b374e04c3480d9cb17dd5c0f02dbee66", "ClientPath": "/opt/dtdrvapp/client", "CqtImageTemperPath": "/opt/dtdrvapp/cqt", "XlsxTemperPath": "/opt/dtdrvapp/xlsx", "MccFilePath": "/opt/dtdrvapp/mcc", "ForciblyUpdate": false}, "ConnectionStrings": {"DefaultConnection": "Server=************,1433;Database=DTASERVER;User Id=dtauser;Password=dtauser;TrustServerCertificate=true;", "MySqlConnection": "Server=your_mysql_server;Port=2881;Database=DtDrvApp;Uid=dtdrvapp_user;Pwd=your_secure_password;"}, "DatabaseSettings": {"DisplaySql": false, "CommandTimeout": 30, "ConnectionTimeout": 30, "MaxPoolSize": 100, "MinPoolSize": 10, "RetryCount": 3, "RetryInterval": 1000}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "DtDrvApp": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning", "DtDrvApp": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/dtdrvapp/dtdrvapp-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/dtdrvapp/dtdrvapp-sql-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "restrictedToMinimumLevel": "Information", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}]}, "AllowedHosts": "*"}