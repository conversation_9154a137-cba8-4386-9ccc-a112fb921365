{"Communication": {"DataPort": 23456, "IsIPv6Enable": false, "ClientLogger": true, "Key": "b374e04c3480d9cb17dd5c0f02dbee66", "ClientPath": "temp/client", "CqtImageTemperPath": "temp/cqt", "XlsxTemperPath": "temp/xlsx", "MccFilePath": "temp/mcc", "ForciblyUpdate": false}, "ConnectionStrings": {"DefaultConnection": "Server=************,1433;Database=DTASERVER;UID=dtauser;PWD=dtauser;TrustServerCertificate=true;", "MySqlConnection": ""}, "DatabaseSettings": {"DisplaySql": true, "CommandTimeout": 60, "ConnectionTimeout": 30, "MaxPoolSize": 50, "MinPoolSize": 2, "RetryCount": 3, "RetryInterval": 1000}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "DtDrvApp": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information", "DtDrvApp": "Debug"}}}}