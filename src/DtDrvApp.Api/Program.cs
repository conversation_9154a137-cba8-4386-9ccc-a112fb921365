using DtDrvApp.Core.Configuration;
using DtDrvApp.Core.Entities;
using DtDrvApp.Core.Interfaces;
using DtDrvApp.Core.Services;
using DtDrvApp.Infrastructure.Data;
using DtDrvApp.Infrastructure.Services;
using Serilog;
using Serilog.Events;
using SqlSugar;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
builder.Host.UseSerilog((context, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .WriteTo.Console()
        .WriteTo.File("logs/dtdrvapp-.log", rollingInterval: RollingInterval.Day)
        .WriteTo.File("logs/dtdrvapp-sql-.log", 
                     restrictedToMinimumLevel: LogEventLevel.Debug,
                     rollingInterval: RollingInterval.Day);
});

// 添加配置选项
builder.Services.Configure<CommunicationOptions>(
    builder.Configuration.GetSection(CommunicationOptions.SectionName));
builder.Services.Configure<DatabaseOptions>(
    builder.Configuration.GetSection(DatabaseOptions.SectionName));

// 配置 SqlSugar
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
var mysqlConnectionString = builder.Configuration.GetConnectionString("MySqlConnection");

ConnectionConfig dbConfig;

if (!string.IsNullOrEmpty(connectionString))
{
    // 使用 SQL Server
    dbConfig = new ConnectionConfig()
    {
        ConnectionString = connectionString,
        DbType = DbType.SqlServer,
        IsAutoCloseConnection = true,
        InitKeyType = InitKeyType.Attribute
    };
}
else if (!string.IsNullOrEmpty(mysqlConnectionString))
{
    // 使用 MySQL
    dbConfig = new ConnectionConfig()
    {
        ConnectionString = mysqlConnectionString,
        DbType = DbType.MySql,
        IsAutoCloseConnection = true,
        InitKeyType = InitKeyType.Attribute
    };
}
else
{
    throw new InvalidOperationException("No database connection string configured. Please configure either DefaultConnection (SQL Server) or MySqlConnection (MySQL).");
}

// 注册 SqlSugar
builder.Services.AddSingleton<ISqlSugarClient>(provider =>
{
    return new SqlSugarClient(dbConfig);
});

// 注册数据库上下文
builder.Services.AddScoped<DtDbContext>();

// 注册核心服务
builder.Services.AddScoped<IDataAccess, SqlSugarDataAccessService>();
builder.Services.AddScoped<IDatabaseInfoManager, DatabaseInfoManager>();
builder.Services.AddScoped<ICommandDispatcher, CommandDispatcher>();
builder.Services.AddScoped<ISocketHandler, SocketHandler>();
builder.Services.AddScoped<IAuthenticationHandler, AuthenticationHandler>();
builder.Services.AddScoped<ICryptoService, DESCryptoService>();

// 注册网络服务
builder.Services.AddSingleton<INetworkService, TcpNetworkService>();

// 注册命令处理器
builder.Services.AddScoped<ICommandHandler, SearchCommandHandler>();
builder.Services.AddScoped<ICommandHandler, DIYSearchCommandHandler>();
builder.Services.AddScoped<ICommandHandler, UserManagementCommandHandler>();
builder.Services.AddScoped<ICommandHandler, ConfigManagementCommandHandler>();
builder.Services.AddScoped<ICommandHandler, DatabaseManagementCommandHandler>();
builder.Services.AddScoped<ICommandHandler, StatisticCommandHandler>();
builder.Services.AddScoped<ICommandHandler, CommunityManagementCommandHandler>();
builder.Services.AddScoped<ICommandHandler, AuthenticationCommandHandler>();
builder.Services.AddScoped<ICommandHandler, TestDataManagementCommandHandler>();

// 注册认证子命令处理器 - 对应C++中的pfunc_AuthenDealData
builder.Services.AddScoped<ICommandHandler, AuthenticationUserCommandHandler>();
builder.Services.AddScoped<ICommandHandler, AuthenticationPasswordCommandHandler>();
builder.Services.AddScoped<ICommandHandler, AuthenticationUser2CommandHandler>();
builder.Services.AddScoped<ICommandHandler, AuthenticationPassword2CommandHandler>();

// 注册主服务
builder.Services.AddHostedService<DtDrvAppMainService>();

// 添加控制器支持（如果需要Web API）
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 添加健康检查
builder.Services.AddHealthChecks();

// 配置Windows服务支持
if (OperatingSystem.IsWindows())
{
    builder.Services.AddWindowsService();
}

// 配置Linux systemd支持
if (OperatingSystem.IsLinux())
{
    builder.Services.AddSystemd();
}

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

// 添加健康检查端点
app.MapHealthChecks("/health");

// 添加基本信息端点
app.MapGet("/", () => new
{
    Application = "DtDrvApp.Core",
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName,
    Timestamp = DateTime.UtcNow
});

// 确保数据库已创建
using (var scope = app.Services.CreateScope())
{
    try
    {
        var context = scope.ServiceProvider.GetService<DtDbContext>();
        if (context != null)
        {
            // 显示当前环境和配置文件信息
            var environment = app.Environment.EnvironmentName;
            Log.Information("Current Environment: {Environment}", environment);

            // 显示正在使用的配置文件
            var configFiles = GetActiveConfigurationFiles(environment);
            Log.Information("Active configuration files: {ConfigFiles}", string.Join(", ", configFiles));

            // 显示配置源详细信息
            LogConfigurationSources(builder.Configuration);

            // 检查数据库配置并显示来源
            var dbConnectionString = builder.Configuration.GetConnectionString("DefaultConnection");
            Log.Information("Database connection string: {ConnectionString}",
                string.IsNullOrEmpty(dbConnectionString) ? "Not configured" :
                dbConnectionString[..Math.Min(50, dbConnectionString.Length)] + "...");

            // 显示连接字符串的来源
            LogConfigurationValueSource(builder.Configuration, "ConnectionStrings:DefaultConnection");

            // 先测试数据库连接
            var isConnected = await context.TestConnectionAsync();

            if (isConnected)
            {
                Log.Information("Database connection successful, initializing tables...");
                await context.InitializeDatabaseAsync();
                Log.Information("Database initialized successfully");
            }
            else
            {
                Log.Warning("Database connection failed. Please check:");
                Log.Warning("1. Database server is running and accessible");
                Log.Warning("2. Connection string is correct");
                Log.Warning("3. Network connectivity to database server");
                Log.Warning("4. Database user permissions");
                Log.Information("Application will continue without database functionality");
            }
        }
        else
        {
            Log.Warning("DtDbContext not available, skipping database initialization");
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Error during database initialization");
        Log.Information("Application will continue without database functionality");
    }
}

Log.Information("Starting DtDrvApp.Core application");

try
{
    await app.RunAsync();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

/// <summary>
/// 获取当前环境下活跃的配置文件列表
/// </summary>
static List<string> GetActiveConfigurationFiles(string environment)
{
    var configFiles = new List<string>();
    var basePath = Directory.GetCurrentDirectory();

    // 基础配置文件
    var baseConfigFile = Path.Combine(basePath, "appsettings.json");
    if (File.Exists(baseConfigFile))
    {
        configFiles.Add("appsettings.json");
    }

    // 环境特定配置文件
    var envConfigFile = Path.Combine(basePath, $"appsettings.{environment}.json");
    if (File.Exists(envConfigFile))
    {
        configFiles.Add($"appsettings.{environment}.json");
    }

    return configFiles;
}

/// <summary>
/// 记录配置源详细信息
/// </summary>
static void LogConfigurationSources(IConfiguration configuration)
{
    if (configuration is IConfigurationRoot configRoot)
    {
        Log.Information("Configuration sources (in order of priority):");
        for (int i = 0; i < configRoot.Providers.Count(); i++)
        {
            var provider = configRoot.Providers.ElementAt(i);
            var providerType = provider.GetType().Name;

            // 尝试获取文件路径（如果是文件配置提供者）
            var source = GetConfigurationSourceInfo(provider);
            Log.Information("  {Index}. {ProviderType}: {Source}", i + 1, providerType, source);
        }
    }
}

/// <summary>
/// 获取配置源信息
/// </summary>
static string GetConfigurationSourceInfo(IConfigurationProvider provider)
{
    var providerType = provider.GetType();

    // 检查是否是 JSON 文件配置提供者
    if (providerType.Name.Contains("Json"))
    {
        // 使用反射获取文件路径
        var sourceProperty = providerType.GetProperty("Source");
        if (sourceProperty != null)
        {
            var source = sourceProperty.GetValue(provider);
            var pathProperty = source?.GetType().GetProperty("Path");
            if (pathProperty != null)
            {
                var path = pathProperty.GetValue(source)?.ToString();
                return path ?? "Unknown path";
            }
        }
    }

    // 检查是否是环境变量配置提供者
    if (providerType.Name.Contains("Environment"))
    {
        return "Environment Variables";
    }

    // 检查是否是命令行配置提供者
    if (providerType.Name.Contains("CommandLine"))
    {
        return "Command Line Arguments";
    }

    return providerType.Name;
}

/// <summary>
/// 显示特定配置值的来源
/// </summary>
static void LogConfigurationValueSource(IConfiguration configuration, string key)
{
    if (configuration is IConfigurationRoot configRoot)
    {
        // 从最高优先级开始查找
        for (int i = configRoot.Providers.Count() - 1; i >= 0; i--)
        {
            var provider = configRoot.Providers.ElementAt(i);
            if (provider.TryGet(key, out var value))
            {
                var source = GetConfigurationSourceInfo(provider);
                Log.Information("Configuration key '{Key}' found in: {Source}", key, source);
                return;
            }
        }
        Log.Warning("Configuration key '{Key}' not found in any source", key);
    }
}
