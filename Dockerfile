# 使用官方的 .NET 8.0 运行时作为基础镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 23456
EXPOSE 5000
EXPOSE 5001

# 使用官方的 .NET 8.0 SDK 作为构建镜像
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制项目文件并还原依赖
COPY ["src/DtDrvApp.Api/DtDrvApp.Api.csproj", "src/DtDrvApp.Api/"]
COPY ["src/DtDrvApp.Core/DtDrvApp.Core.csproj", "src/DtDrvApp.Core/"]
COPY ["src/DtDrvApp.Infrastructure/DtDrvApp.Infrastructure.csproj", "src/DtDrvApp.Infrastructure/"]

RUN dotnet restore "src/DtDrvApp.Api/DtDrvApp.Api.csproj"

# 复制所有源代码
COPY . .

# 构建应用程序
WORKDIR "/src/src/DtDrvApp.Api"
RUN dotnet build "DtDrvApp.Api.csproj" -c Release -o /app/build

# 发布应用程序
FROM build AS publish
RUN dotnet publish "DtDrvApp.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 最终镜像
FROM base AS final
WORKDIR /app

# 创建日志目录
RUN mkdir -p /app/logs

# 复制发布的应用程序
COPY --from=publish /app/publish .

# 设置环境变量
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:5000;https://+:5001

# 创建非 root 用户
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# 启动应用程序
ENTRYPOINT ["dotnet", "DtDrvApp.Api.dll"]
