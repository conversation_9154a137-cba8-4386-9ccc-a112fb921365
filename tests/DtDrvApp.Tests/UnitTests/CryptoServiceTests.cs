using DtDrvApp.Core.Interfaces;
using DtDrvApp.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace DtDrvApp.Tests.UnitTests;

/// <summary>
/// 加密服务单元测试
/// </summary>
public class CryptoServiceTests
{
    private readonly ICryptoService _cryptoService;
    private readonly Mock<ILogger<DESCryptoService>> _mockLogger;

    public CryptoServiceTests()
    {
        _mockLogger = new Mock<ILogger<DESCryptoService>>();
        _cryptoService = new DESCryptoService(_mockLogger.Object);
    }

    [Fact]
    public void DesEncrypt_WithValidInput_ShouldReturnEncryptedString()
    {
        // Arrange
        var plainText = "Hello World";
        var key = "testkey1";

        // Act
        var result = _cryptoService.DesEncrypt(plainText, key);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().NotBe(plainText);
    }

    [Fact]
    public void DesDecrypt_WithValidInput_ShouldReturnOriginalString()
    {
        // Arrange
        var plainText = "Hello World";
        var key = "testkey1";
        var encrypted = _cryptoService.DesEncrypt(plainText, key);

        // Act
        var result = _cryptoService.DesDecrypt(encrypted, key);

        // Assert
        result.Should().Be(plainText);
    }

    [Fact]
    public void Base64Encode_WithValidInput_ShouldReturnBase64String()
    {
        // Arrange
        var input = "Hello World";

        // Act
        var result = _cryptoService.Base64Encode(input);

        // Assert
        result.Should().Be("SGVsbG8gV29ybGQ=");
    }

    [Fact]
    public void Base64Decode_WithValidInput_ShouldReturnOriginalString()
    {
        // Arrange
        var base64Input = "SGVsbG8gV29ybGQ=";

        // Act
        var result = _cryptoService.Base64DecodeToString(base64Input);

        // Assert
        result.Should().Be("Hello World");
    }

    [Fact]
    public void Md5Hash_WithValidInput_ShouldReturnConsistentHash()
    {
        // Arrange
        var input = "Hello World";

        // Act
        var result1 = _cryptoService.Md5Hash(input);
        var result2 = _cryptoService.Md5Hash(input);

        // Assert
        result1.Should().Be(result2);
        result1.Should().HaveLength(32); // MD5 hash is 32 characters
    }

    [Fact]
    public void GenerateRandomKey_WithSpecifiedLength_ShouldReturnKeyOfCorrectLength()
    {
        // Arrange
        var length = 16;

        // Act
        var result = _cryptoService.GenerateRandomKey(length);

        // Assert
        result.Should().HaveLength(length);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void DesEncrypt_WithNullOrEmptyInput_ShouldReturnEmpty(string input)
    {
        // Arrange
        var key = "testkey1";

        // Act
        var result = _cryptoService.DesEncrypt(input, key);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void TripleDesEncrypt_WithValidInput_ShouldReturnEncryptedString()
    {
        // Arrange
        var plainText = "Hello World";
        var key = "testkey123456789012345678"; // 24 bytes for 3DES

        // Act
        var result = _cryptoService.TripleDesEncrypt(plainText, key, Core.Interfaces.CipherMode.TripleECB);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().NotBe(plainText);
    }

    [Fact]
    public void TripleDesDecrypt_WithValidInput_ShouldReturnOriginalString()
    {
        // Arrange
        var plainText = "Hello World";
        var key = "testkey123456789012345678"; // 24 bytes for 3DES
        var encrypted = _cryptoService.TripleDesEncrypt(plainText, key, Core.Interfaces.CipherMode.TripleECB);

        // Act
        var result = _cryptoService.TripleDesDecrypt(encrypted, key, Core.Interfaces.CipherMode.TripleECB);

        // Assert
        result.Should().Be(plainText);
    }
}
