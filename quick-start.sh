#!/bin/bash

# DtDrvApp.Core 快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    DtDrvApp.Core 快速启动向导"
    echo "=================================================="
    echo -e "${NC}"
    echo "这个脚本将帮助您快速启动DtDrvApp.Core项目"
    echo ""
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."

    # 检查 .NET SDK
    DOTNET_CMD=""
    if command -v dotnet &> /dev/null; then
        DOTNET_CMD="dotnet"
    elif [ -f "/home/<USER>/dotnet" ]; then
        DOTNET_CMD="/home/<USER>/dotnet"
        log_info "找到 .NET SDK: /home/<USER>/dotnet"
    else
        log_error ".NET SDK 未找到！"
        echo "请从以下地址下载并安装 .NET 8.0 SDK:"
        echo "https://dotnet.microsoft.com/download/dotnet/8.0"
        exit 1
    fi

    DOTNET_VERSION=$($DOTNET_CMD --version)
    log_success ".NET SDK 版本: $DOTNET_VERSION"
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        log_success "Docker 已安装"
        DOCKER_AVAILABLE=true
    else
        log_warning "Docker 未安装 (可选)"
        DOCKER_AVAILABLE=false
    fi
}

# 选择启动方式
choose_startup_mode() {
    echo ""
    log_info "请选择启动方式:"
    echo "1) 本地开发模式 (需要本地数据库)"
    echo "2) Docker容器模式 (推荐，包含数据库)"
    echo "3) 仅构建项目"
    echo "4) 运行测试"
    echo ""
    
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            start_local_mode
            ;;
        2)
            if [ "$DOCKER_AVAILABLE" = true ]; then
                start_docker_mode
            else
                log_error "Docker 未安装，无法使用容器模式"
                exit 1
            fi
            ;;
        3)
            build_project
            ;;
        4)
            run_tests
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 本地开发模式
start_local_mode() {
    log_info "启动本地开发模式..."
    
    # 检查配置文件
    if [ ! -f "src/DtDrvApp.Api/appsettings.json" ]; then
        log_error "配置文件不存在: src/DtDrvApp.Api/appsettings.json"
        exit 1
    fi
    
    # 还原依赖
    log_info "还原NuGet包..."
    $DOTNET_CMD restore

    # 构建项目
    log_info "构建项目..."
    $DOTNET_CMD build --configuration Debug
    
    # 检查数据库连接
    log_warning "请确保数据库服务已启动并且连接字符串正确配置"
    log_info "配置文件位置: src/DtDrvApp.Api/appsettings.json"
    
    read -p "数据库已准备好了吗? (y/n): " db_ready
    if [ "$db_ready" != "y" ] && [ "$db_ready" != "Y" ]; then
        log_info "请先配置数据库，然后重新运行此脚本"
        show_database_help
        exit 0
    fi
    
    # 启动应用
    log_info "启动应用程序..."
    log_success "应用程序将在以下地址启动:"
    echo "  - HTTP: http://localhost:5000"
    echo "  - HTTPS: https://localhost:5001"
    echo "  - TCP服务: localhost:23456"
    echo "  - 健康检查: http://localhost:5000/health"
    echo "  - API文档: http://localhost:5000/swagger"
    echo ""
    echo "按 Ctrl+C 停止应用程序"
    echo ""
    
    cd src/DtDrvApp.Api
    $DOTNET_CMD run
}

# Docker容器模式
start_docker_mode() {
    log_info "启动Docker容器模式..."
    
    # 检查docker-compose文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml 文件不存在"
        exit 1
    fi
    
    # 构建并启动容器
    log_info "构建Docker镜像..."
    docker-compose build
    
    log_info "启动容器..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose ps
    
    log_success "服务已启动！"
    echo "  - HTTP: http://localhost:5000"
    echo "  - HTTPS: https://localhost:5001"
    echo "  - TCP服务: localhost:23456"
    echo "  - 健康检查: http://localhost:5000/health"
    echo "  - 数据库: localhost:1433 (SQL Server)"
    echo ""
    echo "查看日志: docker-compose logs -f"
    echo "停止服务: docker-compose down"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 清理
    log_info "清理之前的构建..."
    $DOTNET_CMD clean

    # 还原依赖
    log_info "还原NuGet包..."
    $DOTNET_CMD restore

    # 构建
    log_info "构建项目..."
    $DOTNET_CMD build --configuration Release
    
    log_success "构建完成！"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 构建项目
    $DOTNET_CMD build --configuration Debug

    # 运行测试
    log_info "执行单元测试..."
    $DOTNET_CMD test --verbosity normal
    
    log_success "测试完成！"
}

# 显示数据库帮助信息
show_database_help() {
    echo ""
    log_info "数据库配置帮助:"
    echo ""
    echo "1. SQL Server (推荐):"
    echo "   - 使用Docker: docker run -e \"ACCEPT_EULA=Y\" -e \"SA_PASSWORD=YourStrong@Passw0rd\" -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest"
    echo "   - 连接字符串: \"Server=localhost;Database=DtDrvApp;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;\""
    echo ""
    echo "2. MySQL:"
    echo "   - 使用Docker: docker run --name mysql -e MYSQL_ROOT_PASSWORD=YourStrong@Passw0rd -e MYSQL_DATABASE=DtDrvApp -p 3306:3306 -d mysql:8.0"
    echo "   - 连接字符串: \"Server=localhost;Port=3306;Database=DtDrvApp;Uid=root;Pwd=YourStrong@Passw0rd;\""
    echo ""
    echo "3. 初始化数据库:"
    echo "   - 运行脚本: scripts/init-database.sql"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "DtDrvApp.Core 快速启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -l, --local    直接启动本地模式"
    echo "  -d, --docker   直接启动Docker模式"
    echo "  -b, --build    仅构建项目"
    echo "  -t, --test     运行测试"
    echo ""
    echo "示例:"
    echo "  $0              # 交互式选择启动方式"
    echo "  $0 --local      # 直接启动本地模式"
    echo "  $0 --docker     # 直接启动Docker模式"
}

# 主函数
main() {
    case "${1:-interactive}" in
        -h|--help)
            show_help
            ;;
        -l|--local)
            show_welcome
            check_requirements
            start_local_mode
            ;;
        -d|--docker)
            show_welcome
            check_requirements
            if [ "$DOCKER_AVAILABLE" = true ]; then
                start_docker_mode
            else
                log_error "Docker 未安装"
                exit 1
            fi
            ;;
        -b|--build)
            show_welcome
            check_requirements
            build_project
            ;;
        -t|--test)
            show_welcome
            check_requirements
            run_tests
            ;;
        interactive)
            show_welcome
            check_requirements
            choose_startup_mode
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
