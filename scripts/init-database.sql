-- DtDrvApp 数据库初始化脚本
USE master;
GO

-- 创建数据库（如果不存在）
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'DtDrvApp')
BEGIN
    CREATE DATABASE DtDrvApp;
END
GO

USE DtDrvApp;
GO

-- 创建用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL,
        LoginCode nvarchar(50) NOT NULL UNIQUE,
        Password nvarchar(100) NOT NULL,
        Phone nvarchar(20) NULL,
        Comment nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        LastLoginTime datetime2 NULL,
        LastLoginIp nvarchar(45) NULL,
        DatabaseId int NULL
    );
END
GO

-- 创建角色表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE Roles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        RoleName nvarchar(50) NOT NULL,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
END
GO

-- 创建用户角色关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE UserRoles (
        UserId int NOT NULL,
        RoleId int NOT NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        PRIMARY KEY (UserId, RoleId),
        FOREIGN KEY (UserId) REFERENCES Users(Id),
        FOREIGN KEY (RoleId) REFERENCES Roles(Id)
    );
END
GO

-- 创建功能表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Functions' AND xtype='U')
BEGIN
    CREATE TABLE Functions (
        Id int IDENTITY(1,1) PRIMARY KEY,
        FunctionName nvarchar(50) NOT NULL,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
END
GO

-- 创建子功能表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SubFunctions' AND xtype='U')
BEGIN
    CREATE TABLE SubFunctions (
        Id int IDENTITY(1,1) PRIMARY KEY,
        FunctionId int NOT NULL,
        SubFunctionName nvarchar(50) NOT NULL,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (FunctionId) REFERENCES Functions(Id)
    );
END
GO

-- 创建角色功能关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RoleFunctions' AND xtype='U')
BEGIN
    CREATE TABLE RoleFunctions (
        RoleId int NOT NULL,
        FunctionId int NOT NULL,
        SubFunctionId int NOT NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        PRIMARY KEY (RoleId, FunctionId, SubFunctionId),
        FOREIGN KEY (RoleId) REFERENCES Roles(Id),
        FOREIGN KEY (FunctionId) REFERENCES Functions(Id),
        FOREIGN KEY (SubFunctionId) REFERENCES SubFunctions(Id)
    );
END
GO

-- 创建数据库连接表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DatabaseConnections' AND xtype='U')
BEGIN
    CREATE TABLE DatabaseConnections (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ServerAddress nvarchar(100) NOT NULL,
        DatabaseName nvarchar(50) NOT NULL,
        Username nvarchar(50) NOT NULL,
        Password nvarchar(200) NOT NULL, -- 加密存储
        Port int NOT NULL DEFAULT 1433,
        DatabaseType int NOT NULL DEFAULT 0, -- 0: SQL Server, 1: MySQL
        DisplaySql bit NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
END
GO

-- 创建系统配置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemConfigurations' AND xtype='U')
BEGIN
    CREATE TABLE SystemConfigurations (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ConfigKey nvarchar(100) NOT NULL UNIQUE,
        ConfigValue nvarchar(1000) NULL,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
END
GO

-- 创建项目设置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProjectSettings' AND xtype='U')
BEGIN
    CREATE TABLE ProjectSettings (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ProjectId int NOT NULL,
        ProjectName nvarchar(100) NULL,
        TestType int NOT NULL DEFAULT 0,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
END
GO

-- 创建系统操作日志表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemOperationLogs' AND xtype='U')
BEGIN
    CREATE TABLE SystemOperationLogs (
        Id bigint IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL,
        UserIp nvarchar(45) NOT NULL,
        EventTypeId int NOT NULL DEFAULT 0,
        FunctionId int NOT NULL DEFAULT 0,
        SubFunctionId int NOT NULL DEFAULT 0,
        EventDescription nvarchar(1000) NULL,
        CityId int NOT NULL DEFAULT 0,
        OperationTime datetime2 NOT NULL DEFAULT GETUTCDATE()
    );
END
GO

-- 创建用户登录日志表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserLoginLogs' AND xtype='U')
BEGIN
    CREATE TABLE UserLoginLogs (
        Id bigint IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL,
        LoginIp nvarchar(45) NOT NULL,
        LoginTime datetime2 NOT NULL DEFAULT GETUTCDATE(),
        IsSuccess bit NOT NULL DEFAULT 1,
        EventDescription nvarchar(500) NULL
    );
END
GO

-- 创建表配置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TableConfigurations' AND xtype='U')
BEGIN
    CREATE TABLE TableConfigurations (
        Id int IDENTITY(1,1) PRIMARY KEY,
        TableId int NOT NULL,
        TableName nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
END
GO

-- 创建列配置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ColumnConfigurations' AND xtype='U')
BEGIN
    CREATE TABLE ColumnConfigurations (
        Id int IDENTITY(1,1) PRIMARY KEY,
        TableId int NOT NULL,
        ColumnName nvarchar(100) NOT NULL,
        ColumnType nvarchar(50) NULL,
        ColumnOrder int NOT NULL DEFAULT 0,
        MaxLength int NOT NULL DEFAULT 0,
        IsNullable bit NOT NULL DEFAULT 1,
        Description nvarchar(500) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (TableId) REFERENCES TableConfigurations(Id)
    );
END
GO

-- 插入初始数据
-- 插入默认管理员用户（密码: admin123，已加密）
IF NOT EXISTS (SELECT * FROM Users WHERE LoginCode = 'admin')
BEGIN
    INSERT INTO Users (Username, LoginCode, Password, IsActive)
    VALUES ('Administrator', 'admin', 'xLOm6OmcplFIdQ7bffRruQ==', 1);
END
GO

-- 插入默认角色
IF NOT EXISTS (SELECT * FROM Roles WHERE RoleName = 'Administrator')
BEGIN
    INSERT INTO Roles (RoleName, Description)
    VALUES ('Administrator', '系统管理员');
END
GO

-- 插入默认功能
IF NOT EXISTS (SELECT * FROM Functions WHERE FunctionName = 'UserManagement')
BEGIN
    INSERT INTO Functions (FunctionName, Description)
    VALUES ('UserManagement', '用户管理');
END
GO

-- 插入默认系统配置
IF NOT EXISTS (SELECT * FROM SystemConfigurations WHERE ConfigKey = 'PasswordValidDays')
BEGIN
    INSERT INTO SystemConfigurations (ConfigKey, ConfigValue, Description)
    VALUES ('PasswordValidDays', '90', '密码有效天数');
END
GO

IF NOT EXISTS (SELECT * FROM SystemConfigurations WHERE ConfigKey = 'EnableAccountControl')
BEGIN
    INSERT INTO SystemConfigurations (ConfigKey, ConfigValue, Description)
    VALUES ('EnableAccountControl', 'false', '是否启用账户控制');
END
GO

PRINT 'Database initialization completed successfully.';
GO
