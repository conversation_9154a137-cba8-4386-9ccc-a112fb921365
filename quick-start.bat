@echo off
setlocal enabledelayedexpansion

:: DtDrvApp.Core 快速启动脚本 (Windows版本)

title DtDrvApp.Core 快速启动向导

:: 颜色定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 显示欢迎信息
echo %BLUE%==================================================
echo     DtDrvApp.Core 快速启动向导 (Windows)
echo ==================================================%NC%
echo.
echo 这个脚本将帮助您快速启动DtDrvApp.Core项目
echo.

:: 检查系统要求
echo %BLUE%[INFO]%NC% 检查系统要求...

:: 检查 .NET SDK
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% .NET SDK 未找到！
    echo 请从以下地址下载并安装 .NET 8.0 SDK:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo %GREEN%[SUCCESS]%NC% .NET SDK 版本: %DOTNET_VERSION%

:: 检查 Docker (可选)
docker --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Docker 未安装 (可选)
    set DOCKER_AVAILABLE=false
) else (
    echo %GREEN%[SUCCESS]%NC% Docker 已安装
    set DOCKER_AVAILABLE=true
)

:: 选择启动方式
:choose_mode
echo.
echo %BLUE%[INFO]%NC% 请选择启动方式:
echo 1) 本地开发模式 (需要本地数据库)
echo 2) Docker容器模式 (推荐，包含数据库)
echo 3) 仅构建项目
echo 4) 运行测试
echo 5) 显示帮助信息
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto local_mode
if "%choice%"=="2" goto docker_mode
if "%choice%"=="3" goto build_project
if "%choice%"=="4" goto run_tests
if "%choice%"=="5" goto show_help
echo %RED%[ERROR]%NC% 无效选择
goto choose_mode

:: 本地开发模式
:local_mode
echo %BLUE%[INFO]%NC% 启动本地开发模式...

:: 检查配置文件
if not exist "src\DtDrvApp.Api\appsettings.json" (
    echo %RED%[ERROR]%NC% 配置文件不存在: src\DtDrvApp.Api\appsettings.json
    pause
    exit /b 1
)

:: 还原依赖
echo %BLUE%[INFO]%NC% 还原NuGet包...
dotnet restore
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 还原依赖失败
    pause
    exit /b 1
)

:: 构建项目
echo %BLUE%[INFO]%NC% 构建项目...
dotnet build --configuration Debug
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 构建失败
    pause
    exit /b 1
)

:: 检查数据库连接
echo %YELLOW%[WARNING]%NC% 请确保数据库服务已启动并且连接字符串正确配置
echo %BLUE%[INFO]%NC% 配置文件位置: src\DtDrvApp.Api\appsettings.json
echo.

set /p db_ready="数据库已准备好了吗? (y/n): "
if /i not "%db_ready%"=="y" (
    echo %BLUE%[INFO]%NC% 请先配置数据库，然后重新运行此脚本
    call :show_database_help
    pause
    exit /b 0
)

:: 启动应用
echo %BLUE%[INFO]%NC% 启动应用程序...
echo %GREEN%[SUCCESS]%NC% 应用程序将在以下地址启动:
echo   - HTTP: http://localhost:5000
echo   - HTTPS: https://localhost:5001
echo   - TCP服务: localhost:23456
echo   - 健康检查: http://localhost:5000/health
echo   - API文档: http://localhost:5000/swagger
echo.
echo 按 Ctrl+C 停止应用程序
echo.

cd src\DtDrvApp.Api
dotnet run
goto end

:: Docker容器模式
:docker_mode
if "%DOCKER_AVAILABLE%"=="false" (
    echo %RED%[ERROR]%NC% Docker 未安装，无法使用容器模式
    pause
    exit /b 1
)

echo %BLUE%[INFO]%NC% 启动Docker容器模式...

:: 检查docker-compose文件
if not exist "docker-compose.yml" (
    echo %RED%[ERROR]%NC% docker-compose.yml 文件不存在
    pause
    exit /b 1
)

:: 构建并启动容器
echo %BLUE%[INFO]%NC% 构建Docker镜像...
docker-compose build
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker镜像构建失败
    pause
    exit /b 1
)

echo %BLUE%[INFO]%NC% 启动容器...
docker-compose up -d
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 容器启动失败
    pause
    exit /b 1
)

:: 等待服务启动
echo %BLUE%[INFO]%NC% 等待服务启动...
timeout /t 10 /nobreak >nul

:: 检查服务状态
echo %BLUE%[INFO]%NC% 检查服务状态...
docker-compose ps

echo %GREEN%[SUCCESS]%NC% 服务已启动！
echo   - HTTP: http://localhost:5000
echo   - HTTPS: https://localhost:5001
echo   - TCP服务: localhost:23456
echo   - 健康检查: http://localhost:5000/health
echo   - 数据库: localhost:1433 (SQL Server)
echo.
echo 查看日志: docker-compose logs -f
echo 停止服务: docker-compose down
pause
goto end

:: 构建项目
:build_project
echo %BLUE%[INFO]%NC% 构建项目...

:: 清理
echo %BLUE%[INFO]%NC% 清理之前的构建...
dotnet clean

:: 还原依赖
echo %BLUE%[INFO]%NC% 还原NuGet包...
dotnet restore
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 还原依赖失败
    pause
    exit /b 1
)

:: 构建
echo %BLUE%[INFO]%NC% 构建项目...
dotnet build --configuration Release
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 构建失败
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% 构建完成！
pause
goto end

:: 运行测试
:run_tests
echo %BLUE%[INFO]%NC% 运行测试...

:: 构建项目
dotnet build --configuration Debug
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 构建失败
    pause
    exit /b 1
)

:: 运行测试
echo %BLUE%[INFO]%NC% 执行单元测试...
dotnet test --verbosity normal
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% 部分测试失败
) else (
    echo %GREEN%[SUCCESS]%NC% 所有测试通过！
)

pause
goto end

:: 显示数据库帮助信息
:show_database_help
echo.
echo %BLUE%[INFO]%NC% 数据库配置帮助:
echo.
echo 1. SQL Server (推荐):
echo    - 使用Docker: docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=YourStrong@Passw0rd" -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest
echo    - 连接字符串: "Server=localhost;Database=DtDrvApp;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;"
echo.
echo 2. MySQL:
echo    - 使用Docker: docker run --name mysql -e MYSQL_ROOT_PASSWORD=YourStrong@Passw0rd -e MYSQL_DATABASE=DtDrvApp -p 3306:3306 -d mysql:8.0
echo    - 连接字符串: "Server=localhost;Port=3306;Database=DtDrvApp;Uid=root;Pwd=YourStrong@Passw0rd;"
echo.
echo 3. 初始化数据库:
echo    - 运行脚本: scripts\init-database.sql
echo.
goto :eof

:: 显示帮助信息
:show_help
echo DtDrvApp.Core 快速启动脚本 (Windows版本)
echo.
echo 用法: %~nx0
echo.
echo 功能:
echo   1. 本地开发模式 - 在本地运行应用程序
echo   2. Docker容器模式 - 使用Docker容器运行完整环境
echo   3. 构建项目 - 仅构建项目不运行
echo   4. 运行测试 - 执行单元测试
echo.
echo 要求:
echo   - .NET 8.0 SDK
echo   - 数据库 (SQL Server 或 MySQL)
echo   - Docker (可选，用于容器模式)
echo.
pause
goto choose_mode

:end
echo.
echo %BLUE%[INFO]%NC% 脚本执行完成
pause
