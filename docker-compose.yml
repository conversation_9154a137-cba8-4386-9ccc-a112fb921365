version: '3.8'

services:
  dtdrvapp-core:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dtdrvapp-core
    ports:
      - "23456:23456"  # TCP 数据端口
      - "5000:5000"    # HTTP 端口
      - "5001:5001"    # HTTPS 端口
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5000;https://+:5001
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=DtDrvApp;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - sqlserver
    networks:
      - dtdrvapp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: dtdrvapp-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./scripts:/scripts
    networks:
      - dtdrvapp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL 替代方案（可选）
  mysql:
    image: mysql:8.0
    container_name: dtdrvapp-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=YourStrong@Passw0rd
      - MYSQL_DATABASE=DtDrvApp
      - MYSQL_USER=dtdrvapp_user
      - MYSQL_PASSWORD=YourStrong@Passw0rd
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql:/docker-entrypoint-initdb.d
    networks:
      - dtdrvapp-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pYourStrong@Passw0rd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    profiles:
      - mysql

  # Redis 缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: dtdrvapp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dtdrvapp-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - cache

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: dtdrvapp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - dtdrvapp-core
    networks:
      - dtdrvapp-network
    restart: unless-stopped
    profiles:
      - proxy

volumes:
  sqlserver_data:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  dtdrvapp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
